import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  On<PERSON><PERSON>roy,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RouterLink,
  RouterLinkActive,
  Router,
  NavigationEnd,
} from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRippleModule } from '@angular/material/core';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDividerModule } from '@angular/material/divider';
import { ThemePalette } from '@angular/material/core';
import { AuthService } from '../../../core/services/auth.service';
import { BehaviorSubject, Subject, filter, takeUntil } from 'rxjs';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';

interface MenuItem {
  name: string;
  icon: string;
  route?: string;
  children?: MenuItem[];
  expanded?: boolean;
  requiredPermission?: { module: string; action: string };
  badge?: number;
  badgeColor?: ThemePalette;
  addAction?: {
    icon: string;
    route: string;
    tooltip: string;
    requiredPermission?: { module: string; action: string };
  };
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    RouterLinkActive,
    MatIconModule,
    MatTooltipModule,
    MatRippleModule,
    MatBadgeModule,
    MatDividerModule,
  ],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
  animations: [
    trigger('submenuAnimation', [
      state(
        'hidden',
        style({
          height: '0',
          opacity: '0',
          overflow: 'hidden',
          'padding-top': '0',
          'padding-bottom': '0',
          'margin-top': '0',
          'margin-bottom': '0',
        })
      ),
      state(
        'visible',
        style({
          height: '*',
          opacity: '1',
          overflow: 'hidden',
        })
      ),
      transition('hidden <=> visible', [
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)'),
      ]),
    ]),
    trigger('rotateIcon', [
      state('collapsed', style({ transform: 'rotate(0)' })),
      state('expanded', style({ transform: 'rotate(180deg)' })),
      transition('collapsed <=> expanded', [
        animate('225ms cubic-bezier(0.4, 0, 0.2, 1)'),
      ]),
    ]),
  ],
})
export class SidebarComponent implements OnInit, OnDestroy {
  @Input() collapsed = false;
  @Output() collapsedChange = new EventEmitter<boolean>();

  // Track permission status for each menu item
  menuPermissions = new Map<string, BehaviorSubject<boolean>>();
  private destroy$ = new Subject<void>();

  // Track active routes for highlighting
  activeRoutes: string[] = [];
  expandedParents: Set<MenuItem> = new Set();

  menuItems: MenuItem[] = [
    {
      name: 'Dashboard',
      icon: 'dashboard',
      route: '/admin/dashboard',
      requiredPermission: { module: 'dashboard', action: 'read' },
    },
    {
      name: 'Administration',
      icon: 'admin_panel_settings',
      expanded: false,
      children: [
        {
          name: 'Users',
          icon: 'people',
          route: '/admin/users',
          requiredPermission: { module: 'user', action: 'read' },
          addAction: {
            icon: 'person_add',
            route: '/admin/users/new',
            tooltip: 'Add New User',
            requiredPermission: { module: 'user', action: 'create' },
          },
          badge: 2,
          badgeColor: 'accent',
        },
        {
          name: 'Roles',
          icon: 'assignment_ind',
          route: '/admin/roles',
          requiredPermission: { module: 'role', action: 'read' },
          addAction: {
            icon: 'add_moderator',
            route: '/admin/roles/new',
            tooltip: 'Add New Role',
            requiredPermission: { module: 'role', action: 'create' },
          },
        },
        {
          name: 'Modules',
          icon: 'widgets',
          route: '/admin/modules',
          requiredPermission: { module: 'module', action: 'read' },
          addAction: {
            icon: 'add_box',
            route: '/admin/modules/new',
            tooltip: 'Add New Module',
            requiredPermission: { module: 'module', action: 'create' },
          },
        },
        {
          name: 'Permissions',
          icon: 'security',
          route: '/admin/permissions',
          requiredPermission: { module: 'permission', action: 'read' },
        },
      ],
    },
    {
      name: 'Masters',
      icon: 'public',
      expanded: false,
      requiredPermission: { module: 'masters', action: 'read' },
      children: [
        // Geographic & Basic Reference Data
        {
          name: 'Countries',
          icon: 'public',
          route: '/admin/masters/countries',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/countries/new',
            tooltip: 'Add New Country',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'States',
          icon: 'map',
          route: '/admin/masters/states',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/states/new',
            tooltip: 'Add New State',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Cities',
          icon: 'location_city',
          route: '/admin/masters/cities',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/cities/new',
            tooltip: 'Add New City',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Localities',
          icon: 'apartment',
          route: '/admin/masters/localities',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/localities/new',
            tooltip: 'Add New Locality',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Blood Groups',
          icon: 'bloodtype',
          route: '/admin/masters/blood-groups',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/blood-groups/new',
            tooltip: 'Add New Blood Group',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
      ],
    },
    {
      name: 'HR Masters',
      icon: 'groups',
      expanded: false,
      requiredPermission: { module: 'masters', action: 'read' },
      children: [
        // Human Resources & Company Structure
        {
          name: 'Departments',
          icon: 'business',
          route: '/admin/masters/departments',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/departments/new',
            tooltip: 'Add New Department',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Designations',
          icon: 'badge',
          route: '/admin/masters/designations',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/designations/new',
            tooltip: 'Add New Designation',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Employment Types',
          icon: 'work',
          route: '/admin/masters/employment-types',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/employment-types/new',
            tooltip: 'Add New Employment Type',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Qualifications',
          icon: 'school',
          route: '/admin/masters/qualifications',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/qualifications/new',
            tooltip: 'Add New Qualification',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
      ],
    },
    {
      name: 'Product Masters',
      icon: 'inventory',
      expanded: false,
      requiredPermission: { module: 'masters', action: 'read' },
      children: [
        // Products, Suppliers & Projects
        {
          name: 'Supplier Types',
          icon: 'local_shipping',
          route: '/admin/masters/supplier-types',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/supplier-types/new',
            tooltip: 'Add New Supplier Type',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Product Categories',
          icon: 'category',
          route: '/admin/masters/product-categories',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/product-categories/new',
            tooltip: 'Add New Product Category',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Product Subcategories',
          icon: 'subdirectory_arrow_right',
          route: '/admin/masters/product-subcategories',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/product-subcategories/new',
            tooltip: 'Add New Product Subcategory',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'GST Values',
          icon: 'percent',
          route: '/admin/masters/gst-values',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/gst-values/new',
            tooltip: 'Add New GST Value',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Measurement Units',
          icon: 'straighten',
          route: '/admin/masters/measurement-units',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/measurement-units/new',
            tooltip: 'Add New Measurement Unit',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Brands',
          icon: 'branding_watermark',
          route: '/admin/masters/brands',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/brands/new',
            tooltip: 'Add New Brand',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Project Types',
          icon: 'assignment',
          route: '/admin/masters/project-types',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/project-types/new',
            tooltip: 'Add New Project Type',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Project Statuses',
          icon: 'assignment_turned_in',
          route: '/admin/masters/project-statuses',
          requiredPermission: { module: 'masters', action: 'read' },
          addAction: {
            icon: 'add',
            route: '/admin/masters/project-statuses/new',
            tooltip: 'Add New Project Status',
            requiredPermission: { module: 'masters', action: 'create' },
          },
        },
        {
          name: 'Categories',
          icon: 'folder',
          route: '/admin/categories',
          requiredPermission: { module: 'category', action: 'read' },
          addAction: {
            icon: 'create_new_folder',
            route: '/admin/categories/new',
            tooltip: 'Add New Category',
            requiredPermission: { module: 'category', action: 'create' },
          },
        },
        {
          name: 'Products',
          icon: 'inventory_2',
          route: '/admin/products',
          requiredPermission: { module: 'product', action: 'read' },
          addAction: {
            icon: 'add_shopping_cart',
            route: '/admin/products/new',
            tooltip: 'Add New Product',
            requiredPermission: { module: 'product', action: 'create' },
          },
        },
        // Customer Management
        {
          name: 'Customers',
          icon: 'people_alt',
          route: '/admin/customers',
          badge: 3,
          badgeColor: 'warn',
          requiredPermission: { module: 'customer', action: 'read' },
          addAction: {
            icon: 'person_add',
            route: '/admin/customers/new',
            tooltip: 'Add New Customer',
            requiredPermission: { module: 'customer', action: 'create' },
          },
        },
      ],
    },
    {
      name: 'Staff Management',
      icon: 'people',
      expanded: false,
      requiredPermission: { module: 'staff', action: 'read' },
      children: [
        {
          name: 'Staffs',
          icon: 'person',
          route: '/admin/staff-management',
          requiredPermission: { module: 'staff', action: 'read' },
          addAction: {
            icon: 'person_add',
            route: '/admin/staff-management/new',
            tooltip: 'Add New Staff Member',
            requiredPermission: { module: 'staff', action: 'create' },
          },
        },
      ],
    },
    {
      name: 'Settings',
      icon: 'settings',
      route: '/admin/settings',
      requiredPermission: { module: 'settings', action: 'read' },
    },
    {
      name: 'My Profile',
      icon: 'account_circle',
      route: '/admin/profile',
      // No permission required - all users can access their profile
    },
  ];

  constructor(
    public authService: AuthService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Initialize permission checks for all menu items
    this.initializePermissions();

    // Prefetch permissions for better UI responsiveness
    this.prefetchPermissions();

    // Listen to user changes to refresh permissions
    this.authService.currentUser
      .pipe(takeUntil(this.destroy$))
      .subscribe((user) => {
        if (user) {
          // Clear and refresh permissions when user changes
          this.authService.clearPermissionCache();
          setTimeout(() => {
            this.refreshPermissions();
          }, 100);
        }
      });

    // Track route changes to auto-expand parents
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.expandPathToActiveRoute();
      });

    // Initial path expansion
    setTimeout(() => {
      this.expandPathToActiveRoute();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to refresh all permissions
  refreshPermissions(): void {
    this.initializePermissions();
    this.prefetchPermissions();

    // Re-check all menu items
    this.menuItems.forEach((item) => {
      this.checkPermissionForItem(item);
    });

    // Trigger change detection
    this.cdr.detectChanges();
  }

  private initializePermissions(): void {
    // Initialize permission subjects for all menu items
    this.menuItems.forEach((item) => {
      this.initPermissionForItem(item);

      // Also check add action permissions
      if (item.addAction?.requiredPermission) {
        const { module, action } = item.addAction.requiredPermission;
        const key = `${module}:${action}`;
        const initialValue = this.authService.hasRole('admin');
        this.menuPermissions.set(
          key,
          new BehaviorSubject<boolean>(initialValue)
        );
      }
    });
  }

  private initPermissionForItem(item: MenuItem): void {
    if (item.requiredPermission) {
      const key = `${item.requiredPermission.module}:${item.requiredPermission.action}`;
      // Default to true for admin users, false otherwise (until checked)
      const initialValue = this.authService.hasRole('admin');
      this.menuPermissions.set(key, new BehaviorSubject<boolean>(initialValue));
    }

    // Process children recursively
    if (item.children) {
      item.children.forEach((child) => {
        this.initPermissionForItem(child);

        // Also check add action permissions for children
        if (child.addAction?.requiredPermission) {
          const { module, action } = child.addAction.requiredPermission;
          const key = `${module}:${action}`;
          const initialValue = this.authService.hasRole('admin');
          this.menuPermissions.set(
            key,
            new BehaviorSubject<boolean>(initialValue)
          );
        }
      });
    }
  }

  private prefetchPermissions(): void {
    // Prefetch permissions for all menu items including nested ones
    this.menuItems.forEach((item) => {
      this.checkPermissionForItem(item);

      // Check add action permissions
      if (item.addAction?.requiredPermission) {
        this.checkAddActionPermission(item.addAction.requiredPermission);
      }
    });
  }

  private checkPermissionForItem(item: MenuItem): void {
    if (item.requiredPermission) {
      const { module, action } = item.requiredPermission;
      const key = `${module}:${action}`;

      // Skip if user is admin (already has all permissions)
      if (this.authService.hasRole('admin')) {
        this.menuPermissions.get(key)?.next(true);
      } else {
        // Use async permission check for more reliable results
        this.authService.hasPermission(module, action).subscribe({
          next: (hasPermission) => {
            this.menuPermissions.get(key)?.next(hasPermission);
            // Trigger change detection to update UI
            this.cdr.detectChanges();
          },
          error: (error) => {
            this.menuPermissions.get(key)?.next(false);
            this.cdr.detectChanges();
          },
        });
      }
    }

    // Process children recursively
    if (item.children) {
      item.children.forEach((child) => {
        this.checkPermissionForItem(child);

        // Check add action permissions for children
        if (child.addAction?.requiredPermission) {
          this.checkAddActionPermission(child.addAction.requiredPermission);
        }
      });
    }
  }

  private checkAddActionPermission(requiredPermission: {
    module: string;
    action: string;
  }): void {
    const { module, action } = requiredPermission;
    const key = `${module}:${action}`;

    if (this.authService.hasRole('admin')) {
      this.menuPermissions.get(key)?.next(true);
    } else {
      this.authService.hasPermission(module, action).subscribe({
        next: (hasPermission) => {
          this.menuPermissions.get(key)?.next(hasPermission);
          this.cdr.detectChanges();
        },
        error: () => {
          this.menuPermissions.get(key)?.next(false);
          this.cdr.detectChanges();
        },
      });
    }
  }

  toggleSubmenu(item: MenuItem, event?: Event): void {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    if (this.collapsed) {
      return;
    }

    item.expanded = !item.expanded;

    // Close other submenus at the same level if this one is expanded
    if (item.expanded) {
      // Find parent menu item
      const parentMenuItem = this.findParentMenuItem(item);

      if (parentMenuItem) {
        // Close sibling submenus
        parentMenuItem.children?.forEach((sibling) => {
          if (sibling !== item && sibling.expanded) {
            sibling.expanded = false;
          }
        });
      } else {
        // Top-level menu item
        this.menuItems.forEach((menuItem) => {
          if (menuItem !== item && menuItem.expanded) {
            menuItem.expanded = false;
          }
        });
      }
    }
  }

  // Find the parent of a menu item
  private findParentMenuItem(
    childItem: MenuItem,
    currentItems: MenuItem[] = this.menuItems
  ): MenuItem | null {
    for (const item of currentItems) {
      if (item.children?.includes(childItem)) {
        return item;
      }

      if (item.children) {
        const parent = this.findParentMenuItem(childItem, item.children);
        if (parent) return parent;
      }
    }

    return null;
  }

  navigateToAddRoute(route: string, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.router.navigate([route]);
  }

  toggleCollapsed(): void {
    this.collapsed = !this.collapsed;
    this.collapsedChange.emit(this.collapsed);

    // When collapsing, close all expanded submenus
    if (this.collapsed) {
      this.collapseAllMenus();
    }
  }

  collapseAllMenus(): void {
    this.menuItems.forEach((item) => {
      if (item.children) {
        item.expanded = false;
        this.collapseChildren(item.children);
      }
    });
  }

  private collapseChildren(children: MenuItem[]): void {
    children.forEach((child) => {
      if (child.children) {
        child.expanded = false;
        this.collapseChildren(child.children);
      }
    });
  }

  hasPermission(item: MenuItem): boolean {
    // If no permission required, always show the menu item
    if (!item.requiredPermission) return true;

    const { module, action } = item.requiredPermission;
    const key = `${module}:${action}`;

    // Admin check - always has permission
    if (this.authService.hasRole('admin')) {
      return true;
    }

    // Check our permission tracking system
    const permissionSubject = this.menuPermissions.get(key);
    if (permissionSubject) {
      return permissionSubject.value;
    }

    // Fallback to sync check if not in our tracking system
    return this.authService.hasPermissionSync(module, action);
  }

  // Check permission for add action
  hasAddActionPermission(action: {
    requiredPermission?: { module: string; action: string };
  }): boolean {
    if (!action.requiredPermission) return true;

    const { module, action: actionType } = action.requiredPermission;
    const key = `${module}:${actionType}`;

    // Admin check - always has permission
    if (this.authService.hasRole('admin')) {
      return true;
    }

    // Check our permission tracking system
    const permissionSubject = this.menuPermissions.get(key);
    if (permissionSubject) {
      return permissionSubject.value;
    }

    // Fallback to sync check if not in our tracking system
    return this.authService.hasPermissionSync(module, actionType);
  }

  // Check if any child of this item has permission
  hasAnyChildWithPermission(item: MenuItem): boolean {
    if (!item.children) return false;

    return item.children.some((child) => {
      if (this.hasPermission(child)) return true;
      if (child.children) return this.hasAnyChildWithPermission(child);
      return false;
    });
  }

  // Auto-expand parent menus when a child route is active
  expandPathToActiveRoute(): void {
    // Get current route
    const currentRoute = this.router.url;

    // Find and expand all parent items for the current route
    this.expandParentsForRoute(currentRoute);
  }

  // Recursively check and expand parent menus
  private expandParentsForRoute(
    currentRoute: string,
    items: MenuItem[] = this.menuItems
  ): boolean {
    for (const item of items) {
      // Check if this item's route matches current route
      if (
        item.route &&
        (currentRoute === item.route ||
          currentRoute.startsWith(item.route + '/'))
      ) {
        return true;
      }

      // Check add action route
      if (
        item.addAction?.route &&
        (currentRoute === item.addAction.route ||
          currentRoute.startsWith(item.addAction.route + '/'))
      ) {
        return true;
      }

      // Check children if any
      if (item.children) {
        if (this.expandParentsForRoute(currentRoute, item.children)) {
          // If a child is active, expand this parent
          item.expanded = true;
          return true;
        }
      }
    }

    return false;
  }

  // Check if a menu item or any of its children has the active route
  isMenuActive(item: MenuItem): boolean {
    const currentRoute = this.router.url;

    // Direct match
    if (
      item.route &&
      (currentRoute === item.route || currentRoute.startsWith(item.route + '/'))
    ) {
      return true;
    }

    // Check add action route
    if (
      item.addAction?.route &&
      (currentRoute === item.addAction.route ||
        currentRoute.startsWith(item.addAction.route + '/'))
    ) {
      return true;
    }

    // Check children
    if (item.children) {
      return item.children.some((child) => this.isMenuActive(child));
    }

    return false;
  }

  // Check if specifically the add route is active
  isAddRouteActive(item: MenuItem): boolean {
    if (!item.addAction?.route) return false;

    const currentRoute = this.router.url;
    return (
      currentRoute === item.addAction.route ||
      currentRoute.startsWith(item.addAction.route + '/')
    );
  }

  // Safely get the badge number with null check
  getBadgeNumber(item: MenuItem): number | null {
    return item.badge || null;
  }
}
