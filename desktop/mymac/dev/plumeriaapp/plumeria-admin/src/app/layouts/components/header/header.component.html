<header class="header">
  <div class="header-left">
    <button class="menu-toggle" mat-icon-button (click)="onToggleSidenav()">
      <mat-icon>menu</mat-icon>
    </button>
    <div class="page-title">
      <!-- This will be dynamically set based on the current route -->
      Admin Dashboard
    </div>
  </div>
  
  <div class="header-right">
    <!-- Search button -->
    <button mat-icon-button class="action-button">
      <mat-icon>search</mat-icon>
    </button>
    
    <!-- Notifications -->
    <button mat-icon-button [matMenuTriggerFor]="notificationsMenu" class="action-button">
      <mat-icon [matBadge]="getNotificationCount()" matBadgeColor="warn">notifications</mat-icon>
    </button>
    
    <!-- User menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-button">
      <div class="user-avatar">{{ getInitials() }}</div>
    </button>
  </div>
  
  <!-- Notifications menu -->
  <mat-menu #notificationsMenu="matMenu" xPosition="before" class="notification-menu">
    <div class="menu-header">
      <h3>Notifications</h3>
    </div>
    <div class="notification-list">
      <ng-container *ngIf="notifications.length > 0; else noNotifications">
        <a mat-menu-item class="notification-item" *ngFor="let notification of notifications">
          <div class="notification-icon" [ngClass]="notification.type">
            <mat-icon>{{ notification.icon }}</mat-icon>
          </div>
          <div class="notification-content">
            <p class="notification-text">{{ notification.text }}</p>
            <p class="notification-time">{{ notification.time }}</p>
          </div>
        </a>
      </ng-container>
      <ng-template #noNotifications>
        <div class="no-notifications">
          <mat-icon>notifications_off</mat-icon>
          <p>No notifications</p>
        </div>
      </ng-template>
    </div>
    <div class="menu-footer">
      <a mat-button color="primary" routerLink="/admin/notifications">View all</a>
      <button mat-button (click)="markAllAsRead()">Mark all as read</button>
    </div>
  </mat-menu>
  
  <!-- User menu -->
  <mat-menu #userMenu="matMenu" xPosition="before" class="user-menu">
    <div class="menu-header user-header">
      <div class="user-avatar large">{{ getInitials() }}</div>
      <div class="user-details">
        <h3>{{ (authService.currentUser | async)?.name || 'Administrator' }}</h3>
        <p>{{ (authService.currentUser | async)?.email || '<EMAIL>' }}</p>
      </div>
    </div>
    <mat-divider></mat-divider>
    <button mat-menu-item routerLink="/admin/profile">
      <mat-icon>person</mat-icon>
      <span>My Profile</span>
    </button>
    <button mat-menu-item routerLink="/admin/settings">
      <mat-icon>settings</mat-icon>
      <span>Settings</span>
    </button>
    <mat-divider></mat-divider>
    <button mat-menu-item (click)="logout()">
      <mat-icon>exit_to_app</mat-icon>
      <span>Logout</span>
    </button>
  </mat-menu>
</header>