@import 'variables';

// Card container mixin
@mixin card-container {
  padding: $spacing-lg;
  
  @media (max-width: $breakpoint-sm) {
    padding: $spacing-md;
  }
}

// Card styling mixin
@mixin card-styling {
  margin-bottom: $card-margin-bottom;
  border-radius: $card-border-radius;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// Card header mixin
@mixin card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  padding: $card-header-padding;
  
  mat-card-title {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
    color: $text-color;
  }
  
  .header-actions {
    display: flex;
    gap: $spacing-sm;
  }
}

// Loading spinner mixin
@mixin loading-spinner {
  display: flex;
  justify-content: center;
  margin: $spacing-xl 0;
}

// Error message mixin
@mixin error-message {
  background-color: $error-bg;
  color: $error-color;
  padding: $spacing-md;
  border-radius: 4px;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;
  
  mat-icon {
    color: $error-color;
  }
  
  p {
    margin-top: $spacing-sm;
    margin-bottom: $spacing-xs;
  }
  
  ul {
    margin-top: 0;
  }
  
  .error-actions {
    display: flex;
    gap: $spacing-sm;
    margin-top: $spacing-sm;
  }
}

// Success message mixin
@mixin success-message {
  background-color: $success-bg;
  color: $success-color;
  padding: $spacing-md;
  border-radius: 4px;
  margin-bottom: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  
  mat-icon {
    color: $success-color;
  }
}

// No data message mixin
@mixin no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl 0;
  color: $text-lighter-color;
  text-align: center;
  
  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: $spacing-md;
    color: lighten($text-lighter-color, 15%);
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// Form styling mixin
@mixin form-styling {
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  
  .form-row {
    display: flex;
    flex-direction: column;
    
    mat-form-field {
      width: $form-field-width;
    }
  }
  
  .status-toggle {
    margin: $spacing-sm 0;
  }
}

// Table container mixin
@mixin table-container {
  overflow-x: auto;
  
  table {
    width: 100%;
    min-width: $table-min-width;
  }
  
  th.mat-header-cell {
    font-weight: $table-header-font-weight;
    color: $table-header-color;
  }
  
  .status-active {
    color: $status-active-color;
    font-weight: 500;
  }
  
  .status-inactive {
    color: $status-inactive-color;
    font-weight: 500;
  }
  
  @media (max-width: $breakpoint-sm) {
    table {
      min-width: 600px;
    }
  }
}

// Detail item mixin
@mixin detail-item {
  margin-bottom: $spacing-md;
  display: flex;
  
  .label {
    font-weight: 500;
    min-width: 120px;
    color: $text-light-color;
  }
  
  .value {
    flex: 1;
  }
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    
    .label {
      min-width: auto;
      margin-bottom: $spacing-xs;
    }
  }
}
