<div class="container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit' : 'Create' }} Brand</mat-card-title>
    </mat-card-header>

    <div class="loading-shade" *ngIf="loading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="!loading && errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" routerLink="..">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
    </div>

    <mat-card-content *ngIf="!loading && !errorMessage">
      <form [formGroup]="brandForm" (ngSubmit)="onSubmit()">
        <div class="form-field">
          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput
                   formControlName="name"
                   placeholder="Enter brand name" required>
            <mat-error *ngIf="hasFieldError('name')">
              {{ getFieldError('name') }}
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-field">
          <mat-form-field appearance="outline">
            <mat-label>Product Subcategory</mat-label>
            <mat-select formControlName="product_subcategory_id"
                        [disabled]="loadingSubcategories">
              <mat-option *ngIf="loadingSubcategories" disabled>
                Loading subcategories...
              </mat-option>
              <mat-option *ngFor="let subcategory of productSubcategories"
                          [value]="subcategory.id">
                {{ subcategory.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="hasFieldError('product_subcategory_id')">
              {{ getFieldError('product_subcategory_id') }}
            </mat-error>
            <mat-hint>Select the product subcategory this brand belongs to</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-field toggle-field">
          <mat-slide-toggle formControlName="is_active" color="primary">
            Active
          </mat-slide-toggle>
        </div>

        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit" [disabled]="brandForm.invalid || submitting">
            {{ submitting ? 'Saving...' : 'Save' }}
          </button>
          <button mat-button type="button" (click)="onCancel()">
            Cancel
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
