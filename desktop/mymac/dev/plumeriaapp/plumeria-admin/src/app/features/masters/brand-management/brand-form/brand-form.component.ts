import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  BrandService,
  Brand,
} from '../../../../core/services/masters/brand.service';
import {
  ProductSubcategoryService,
  ProductSubcategory,
} from '../../../../core/services/masters/product-subcategory.service';

@Component({
  selector: 'app-brand-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
  ],
  templateUrl: './brand-form.component.html',
  styleUrls: ['./brand-form.component.scss'],
})
export class BrandFormComponent implements OnInit {
  brandForm: FormGroup;
  isEditMode = false;
  brandId: number | null = null;
  loading = false;
  submitting = false;
  errorMessage = '';

  productSubcategories: ProductSubcategory[] = [];
  loadingSubcategories = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private brandService: BrandService,
    private productSubcategoryService: ProductSubcategoryService,
    private snackBar: MatSnackBar
  ) {
    this.brandForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadProductSubcategories();

    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.isEditMode = true;
        this.brandId = +params['id'];
        this.loadBrand();
      }
    });
  }

  createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      product_subcategory_id: ['', [Validators.required]],
      is_active: [true],
    });
  }

  loadProductSubcategories(): void {
    this.loadingSubcategories = true;
    this.productSubcategoryService.getAllProductSubcategories(false).subscribe({
      next: (subcategories) => {
        this.productSubcategories = subcategories;
        this.loadingSubcategories = false;
      },
      error: (error) => {
        this.snackBar.open(
          `Failed to load product subcategories: ${error.message}`,
          'Close',
          {
            duration: 5000,
          }
        );
        this.loadingSubcategories = false;
      },
    });
  }

  loadBrand(): void {
    if (!this.brandId) return;

    this.loading = true;
    this.errorMessage = '';

    this.brandService.getBrandById(this.brandId).subscribe({
      next: (brand) => {
        this.brandForm.patchValue({
          name: brand.name,
          product_subcategory_id: brand.product_subcategory_id,
          is_active: brand.is_active,
        });
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load brand: ${error.message}`;
        this.loading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.brandForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.submitting = true;
    const formValue = this.brandForm.value;

    const brandData: Brand = {
      name: formValue.name.trim(),
      product_subcategory_id: formValue.product_subcategory_id,
      is_active: formValue.is_active,
    };

    const operation = this.isEditMode
      ? this.brandService.updateBrand(this.brandId!, brandData)
      : this.brandService.createBrand(brandData);

    operation.subscribe({
      next: (brand) => {
        const message = this.isEditMode
          ? 'Brand updated successfully'
          : 'Brand created successfully';

        this.snackBar.open(message, 'Close', { duration: 3000 });
        this.router.navigate(['..'], { relativeTo: this.route });
      },
      error: (error) => {
        this.snackBar.open(
          `Failed to ${this.isEditMode ? 'update' : 'create'} brand: ${
            error.message
          }`,
          'Close',
          {
            duration: 5000,
          }
        );
        this.submitting = false;
      },
    });
  }

  onCancel(): void {
    this.router.navigate(['..'], { relativeTo: this.route });
  }

  private markFormGroupTouched(): void {
    Object.keys(this.brandForm.controls).forEach((key) => {
      const control = this.brandForm.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for template
  getFieldError(fieldName: string): string {
    const field = this.brandForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (field.errors['maxlength']) {
        return `${this.getFieldLabel(fieldName)} cannot exceed ${
          field.errors['maxlength'].requiredLength
        } characters`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Brand name',
      product_subcategory_id: 'Product subcategory',
    };
    return labels[fieldName] || fieldName;
  }

  hasFieldError(fieldName: string): boolean {
    const field = this.brandForm.get(fieldName);
    return !!(field?.errors && field.touched);
  }
}
