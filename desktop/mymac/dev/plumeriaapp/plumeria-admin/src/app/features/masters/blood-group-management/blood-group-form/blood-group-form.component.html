<div class="blood-group-form-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit Blood Group' : 'Add New Blood Group' }}</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <form [formGroup]="bloodGroupForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Blood Group Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter blood group name">
            <mat-error *ngIf="nameControl?.hasError('required')">
              Blood group name is required
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('minlength')">
              Blood group name must be at least 2 characters
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('maxlength')">
              Blood group name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Code field removed -->

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ bloodGroupForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button type="button" routerLink="..">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || bloodGroupForm.invalid" (click)="onSubmit()">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>
