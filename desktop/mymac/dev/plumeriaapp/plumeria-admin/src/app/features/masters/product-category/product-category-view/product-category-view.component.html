<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Product Category Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" [routerLink]="['../edit', productCategory.id]" matTooltip="Edit Product Category" *ngIf="productCategory">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="accent" (click)="toggleProductCategoryStatus()" matTooltip="{{ productCategory.is_active ? 'Deactivate' : 'Activate' }}" *ngIf="productCategory">
          <mat-icon>{{ productCategory.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
          {{ productCategory.is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button mat-raised-button color="warn" (click)="deleteProductCategory()" matTooltip="Delete Product Category">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-icon-button routerLink="../.." matTooltip="Back to List">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>
    </mat-card-header>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="!isLoading && errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" routerLink="../..">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
    </div>

    <mat-card-content *ngIf="!isLoading && !errorMessage && productCategory">
      <div class="detail-section">
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ productCategory.id }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ productCategory.name }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Description:</div>
          <div class="detail-value">{{ productCategory.description || 'N/A' }}</div>
        </div>

        <div class="detail-row">
          <div class="detail-label">Status:</div>
          <div class="detail-value">
            <mat-chip [ngClass]="productCategory.is_active ? 'active-chip' : 'inactive-chip'">
              {{ productCategory.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ productCategory.created_by_username || 'N/A' }}</div>
        </div>

        <div class="detail-row" *ngIf="productCategory.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ productCategory.created_at | date:'medium' }}</div>
        </div>

        <div class="detail-row" *ngIf="productCategory.updated_by_username">
          <div class="detail-label">Updated By:</div>
          <div class="detail-value">{{ productCategory.updated_by_username }}</div>
        </div>

        <div class="detail-row" *ngIf="productCategory.updated_at">
          <div class="detail-label">Updated At:</div>
          <div class="detail-value">{{ productCategory.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
