// src/app/features/role-management/role-detail/role-detail.component.scss

.role-detail-container {
    padding: 24px;
  
    .header-actions {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
    }
  
    mat-card {
      margin-bottom: 24px;
    }
  
    .role-info {
      margin-bottom: 24px;
      
      h3 {
        margin-top: 16px;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 500;
      }
      
      .detail-item {
        margin-bottom: 12px;
        display: flex;
        
        .label {
          font-weight: 500;
          width: 120px;
        }
        
        .value {
          flex: 1;
        }
      }
    }
  
    .users-with-role {
      margin-top: 24px;
      
      h3 {
        margin-top: 16px;
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 500;
      }
      
      .no-users {
        font-style: italic;
        color: rgba(0, 0, 0, 0.54);
      }
    }
  
    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      text-align: center;
    }
  
    .error-message {
      color: #f44336;
      text-align: center;
      padding: 40px;
    }
  }