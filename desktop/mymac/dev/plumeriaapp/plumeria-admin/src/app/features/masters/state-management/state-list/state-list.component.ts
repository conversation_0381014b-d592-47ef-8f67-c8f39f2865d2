import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { SelectionModel } from '@angular/cdk/collections';

import { StateService } from '../../../../core/services/masters/state.service';
import { CountryService } from '../../../../core/services/masters/country.service';
import { State } from '../../../../core/models/masters/state';
import { Country } from '../../../../core/models/masters/country';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-state-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
    MatSelectModule
  ],
  templateUrl: './state-list.component.html',
  styleUrls: ['./state-list.component.scss']
})
export class StateListComponent implements OnInit {
  states: State[] = [];
  filteredStates: State[] = [];
  displayedStates: State[] = []; // States after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'code',
    'country_name',
    'created_by',
    'status',
    'actions'
  ];
  selection = new SelectionModel<State>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;
  
  // Country filter
  countries: Country[] = [];
  selectedCountryId: number | null = null;
  loadingCountries = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalStates = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private stateService: StateService,
    private countryService: CountryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadCountries();
    this.loadStates();
  }

  loadCountries(): void {
    this.loadingCountries = true;
    
    this.countryService.getCountries(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingCountries = false;
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        this.loadingCountries = false;
      }
    });
  }

  loadStates(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.stateService.getStates(this.includeInactive, this.selectedCountryId || undefined).subscribe({
      next: (response) => {
        if (response.success) {
          this.states = Array.isArray(response.data) ? response.data : [];
          this.totalStates = this.states.length;
          this.applyFilter();
        } else {
          this.errorMessage = response.message || 'Failed to load states';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading states: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading states:', error);
      }
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();
    
    this.filteredStates = this.states.filter(state => 
      state.name.toLowerCase().includes(filterValue) ||
      state.code.toLowerCase().includes(filterValue) ||
      (state.country_name && state.country_name.toLowerCase().includes(filterValue)) ||
      (state.created_by_username && state.created_by_username.toLowerCase().includes(filterValue))
    );
    
    this.totalStates = this.filteredStates.length;
    
    if (this.paginator) {
      this.paginator.firstPage();
    }
    
    this.updateDisplayedStates();
  }

  updateDisplayedStates(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedStates = this.filteredStates.slice(startIndex, endIndex);
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedStates();
  }

  toggleStatus(state: State): void {
    const newStatus = !state.is_active;
    
    this.stateService.toggleStateStatus(state.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          state.is_active = newStatus;
          this.showSnackBar(`State ${state.name} ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.showSnackBar(`Failed to update status: ${response.message}`, true);
        }
      },
      error: (error) => {
        this.showSnackBar(`Error updating status: ${this.getErrorMessage(error)}`, true);
        console.error('Error toggling state status:', error);
      }
    });
  }

  deleteState(state: State): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the state "${state.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.stateService.deleteState(state.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.states = this.states.filter(s => s.id !== state.id);
              this.applyFilter();
              this.showSnackBar(`State ${state.name} deleted successfully`);
            } else {
              this.showSnackBar(`Failed to delete state: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting state: ${this.getErrorMessage(error)}`, true);
            console.error('Error deleting state:', error);
          }
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedStates = this.selection.selected;
    
    if (selectedStates.length === 0) {
      this.showSnackBar('No states selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedStates.length} selected states?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const ids = selectedStates.map(state => state.id);
        
        this.stateService.bulkDeleteStates(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.states = this.states.filter(state => !ids.includes(state.id));
              this.selection.clear();
              this.applyFilter();
              this.showSnackBar(`Successfully deleted ${ids.length} states`);
            } else {
              this.showSnackBar(`Failed to delete states: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting states: ${this.getErrorMessage(error)}`, true);
            console.error('Error bulk deleting states:', error);
          }
        });
      }
    });
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedStates.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedStates);
    }
  }

  refreshList(): void {
    this.loadStates();
  }

  toggleIncludeInactive(): void {
    this.loadStates();
  }

  onCountryChange(): void {
    this.loadStates();
  }

  clearCountryFilter(): void {
    this.selectedCountryId = null;
    this.loadStates();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }
}
