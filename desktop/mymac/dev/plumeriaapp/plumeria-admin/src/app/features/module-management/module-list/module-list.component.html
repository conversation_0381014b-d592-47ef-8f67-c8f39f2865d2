<!-- src/app/features/module-management/module-list/module-list.component.html -->

<div class="module-list-container">
  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="successMessage" class="success-message">
    {{ successMessage }}
  </div>

  <div class="filter-container">
    <mat-form-field appearance="outline">
      <mat-label>Search Modules</mat-label>
      <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, description, or creator">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <div class="action-buttons">
    <div class="left-actions">
      <button mat-raised-button color="warn" (click)="deleteSelectedModules()"
              [disabled]="selection.selected.length === 0"
              matTooltip="Delete selected modules">
        <mat-icon>delete</mat-icon> Delete Selected ({{ selection.selected.length }})
      </button>
    </div>
    <div class="right-actions">
      <button mat-raised-button color="primary" [routerLink]="['/admin/modules/new']">
        <mat-icon>add</mat-icon> Add New Module
      </button>
    </div>
  </div>

  <table mat-table [dataSource]="displayedModules" class="mat-elevation-z8" *ngIf="!isLoading && modules.length > 0">
    <!-- Checkbox Column -->
    <ng-container matColumnDef="select">
      <th mat-header-cell *matHeaderCellDef>
        <mat-checkbox (change)="$event ? toggleAllRows() : null"
                      [checked]="selection.hasValue() && isAllSelected()"
                      [indeterminate]="selection.hasValue() && !isAllSelected()"
                      [aria-label]="checkboxLabel()">
        </mat-checkbox>
      </th>
      <td mat-cell *matCellDef="let row">
        <mat-checkbox (click)="$event.stopPropagation()"
                      (change)="$event ? selection.toggle(row) : null"
                      [checked]="selection.isSelected(row)"
                      [aria-label]="checkboxLabel(row)">
        </mat-checkbox>
      </td>
    </ng-container>

    <!-- ID Column -->
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef>ID</th>
      <td mat-cell *matCellDef="let module">{{ module.id }}</td>
    </ng-container>

    <!-- Name Column -->
    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef>Name</th>
      <td mat-cell *matCellDef="let module">{{ module.name }}</td>
    </ng-container>

    <!-- Description Column -->
    <ng-container matColumnDef="description">
      <th mat-header-cell *matHeaderCellDef>Description</th>
      <td mat-cell *matCellDef="let module">{{ module.description || 'No description' }}</td>
    </ng-container>

    <!-- Created By Column -->
    <ng-container matColumnDef="created_by">
      <th mat-header-cell *matHeaderCellDef>Created By</th>
      <td mat-cell *matCellDef="let module">{{ module.created_by_username || 'N/A' }}</td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Status</th>
      <td mat-cell *matCellDef="let module">
        <span [ngClass]="module.is_active ? 'status-active' : 'status-inactive'">
          {{ module.is_active ? 'Active' : 'Inactive' }}
        </span>
      </td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef>Actions</th>
      <td mat-cell *matCellDef="let module">
        <button mat-icon-button [routerLink]="['/admin/modules', module.id]" matTooltip="View Details">
          <mat-icon>visibility</mat-icon>
        </button>
        <button mat-icon-button [routerLink]="['/admin/modules/edit', module.id]" matTooltip="Edit">
          <mat-icon>edit</mat-icon>
        </button>
        <button mat-icon-button (click)="toggleModuleStatus(module.id, module.is_active)"
                [matTooltip]="module.is_active ? 'Deactivate' : 'Activate'">
          <mat-icon>{{ module.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
        </button>
        <button mat-icon-button (click)="deleteModule(module.id)" matTooltip="Delete">
          <mat-icon>delete</mat-icon>
        </button>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>

  <mat-paginator
    *ngIf="!isLoading && modules.length > 0"
    [length]="totalModules"
    [pageSize]="pageSize"
    [pageSizeOptions]="pageSizeOptions"
    (page)="onPageChange($event)"
    showFirstLastButtons>
  </mat-paginator>

  <div *ngIf="!isLoading && modules.length === 0" class="no-data">
    No modules found. Click "Add New Module" to create one.
  </div>
</div>
