import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSelectModule } from '@angular/material/select';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  ProductSubcategoryService,
  ProductSubcategory,
} from '../../../../core/services/masters/product-subcategory.service';
import {
  ProductCategoryService,
  ProductCategory,
} from '../../../../core/services/masters/product-category.service';

@Component({
  selector: 'app-product-subcategory-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSelectModule,
    RouterModule,
  ],
  templateUrl: './product-subcategory-form.component.html',
  styleUrl: './product-subcategory-form.component.scss',
})
export class ProductSubcategoryFormComponent implements OnInit {
  productSubcategoryForm!: FormGroup;
  productCategories: ProductCategory[] = [];
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  productSubcategoryId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private productSubcategoryService: ProductSubcategoryService,
    private productCategoryService: ProductCategoryService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadProductCategories();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.productSubcategoryId = +id;
        this.isEditMode = true;
        this.loadProductSubcategory(this.productSubcategoryId);
      }
    });
  }

  initForm(): void {
    this.productSubcategoryForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      category_id: ['', Validators.required],
      description: ['', Validators.maxLength(255)],
      is_active: [true],
    });
  }

  loadProductCategories(): void {
    this.productCategoryService.getAllProductCategories(true).subscribe({
      next: (categories) => {
        this.productCategories = categories.filter((cat) => cat.is_active);
      },
      error: (error) => {
        console.error('Error loading product categories:', error);
        this.snackBar.open('Failed to load product categories', 'Close', {
          duration: 3000,
        });
      },
    });
  }

  loadProductSubcategory(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.productSubcategoryService.getProductSubcategoryById(id).subscribe({
      next: (productSubcategory) => {
        this.productSubcategoryForm.patchValue({
          name: productSubcategory.name,
          category_id: productSubcategory.category_id,
          description: productSubcategory.description || '',
          is_active: productSubcategory.is_active,
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load product subcategory: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.productSubcategoryForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const productSubcategoryData: ProductSubcategory = {
      name: this.productSubcategoryForm.value.name,
      category_id: this.productSubcategoryForm.value.category_id,
      description: this.productSubcategoryForm.value.description,
      is_active: this.productSubcategoryForm.value.is_active,
    };

    if (this.isEditMode && this.productSubcategoryId) {
      this.updateProductSubcategory(
        this.productSubcategoryId,
        productSubcategoryData
      );
    } else {
      this.createProductSubcategory(productSubcategoryData);
    }
  }

  createProductSubcategory(productSubcategoryData: ProductSubcategory): void {
    this.productSubcategoryService
      .createProductSubcategory(productSubcategoryData)
      .subscribe({
        next: (createdProductSubcategory) => {
          this.isSubmitting = false;
          this.snackBar.open(
            'Product subcategory created successfully',
            'Close',
            {
              duration: 3000,
            }
          );
          this.router.navigate(['..'], { relativeTo: this.route });
        },
        error: (error) => {
          this.errorMessage = `Failed to create product subcategory: ${error.message}`;
          this.isSubmitting = false;
        },
      });
  }

  updateProductSubcategory(
    id: number,
    productSubcategoryData: ProductSubcategory
  ): void {
    this.productSubcategoryService
      .updateProductSubcategory(id, productSubcategoryData)
      .subscribe({
        next: (updatedProductSubcategory) => {
          this.isSubmitting = false;
          this.snackBar.open(
            'Product subcategory updated successfully',
            'Close',
            {
              duration: 3000,
            }
          );
          this.router.navigate(['..'], { relativeTo: this.route });
        },
        error: (error) => {
          this.errorMessage = `Failed to update product subcategory: ${error.message}`;
          this.isSubmitting = false;
        },
      });
  }

  cancel(): void {
    this.router.navigate(['..'], { relativeTo: this.route });
  }
}
