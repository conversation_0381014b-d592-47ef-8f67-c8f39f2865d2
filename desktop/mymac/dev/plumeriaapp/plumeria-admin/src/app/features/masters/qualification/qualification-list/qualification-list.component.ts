import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import { QualificationService } from '../../../../core/services/masters/qualification.service';
import { Qualification } from '../../../../core/models/masters/qualification';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-qualification-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './qualification-list.component.html',
  styleUrls: ['./qualification-list.component.scss'],
})
export class QualificationListComponent implements OnInit {
  qualifications: Qualification[] = [];
  filteredQualifications: Qualification[] = [];
  displayedQualifications: Qualification[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<Qualification>(true, []);
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = true; // Set to true to include inactive by default

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalQualifications = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private qualificationService: QualificationService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadQualifications();
  }

  loadQualifications(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.qualificationService
      .getAllQualifications(this.includeInactive)
      .subscribe({
        next: (qualifications) => {
          this.qualifications = qualifications;
          this.totalQualifications = qualifications.length;
          this.applyFilter();
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = `Failed to load qualifications: ${error.message}`;
          this.isLoading = false;
        },
      });
  }

  toggleIncludeInactive(): void {
    // Reload qualifications when the toggle changes
    this.loadQualifications();
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredQualifications = this.qualifications.filter(
      (quali) =>
        quali.name.toLowerCase().includes(filterValue) ||
        (quali.created_by_username &&
          quali.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalQualifications = this.filteredQualifications.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedQualifications();
  }

  updateDisplayedQualifications(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedQualifications = this.filteredQualifications.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedQualifications();
  }

  toggleStatus(qualification: Qualification): void {
    const newStatus = !qualification.is_active;

    this.qualificationService
      .toggleQualificationStatus(qualification.id!, newStatus)
      .subscribe({
        next: (updatedType) => {
          qualification.is_active = updatedType.is_active;
          this.showSnackBar(
            `Qualification ${qualification.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        },
        error: (error) => {
          this.showSnackBar(`Error: ${error.message}`, true);
          // Revert toggle in UI
          qualification.is_active = !newStatus;
        },
      });
  }

  deleteQualification(qualification: Qualification): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the qualification "${qualification.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.qualificationService
          .deleteQualification(qualification.id!)
          .subscribe({
            next: () => {
              this.qualifications = this.qualifications.filter(
                (c) => c.id !== qualification.id
              );
              this.applyFilter();
              this.showSnackBar(
                `Qualification ${qualification.name} deleted successfully`
              );
            },
            error: (error) => {
              this.showSnackBar(`Error: ${error.message}`, true);
            },
          });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedQualifications = this.selection.selected;

    if (selectedQualifications.length === 0) {
      this.showSnackBar('No qualifications selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedQualifications.length} selected qualifications?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedQualifications.map((type) => type.id!);

        this.qualificationService.bulkDeleteQualifications(ids).subscribe({
          next: () => {
            this.qualifications = this.qualifications.filter(
              (type) => !ids.includes(type.id!)
            );
            this.selection.clear();
            this.applyFilter();
            this.showSnackBar(
              `Successfully deleted ${ids.length} qualifications`
            );
          },
          error: (error) => {
            this.showSnackBar(`Error: ${error.message}`, true);
          },
        });
      }
    });
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedQualifications.length;
    return numSelected === numRows && numRows > 0;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedQualifications);
    }
  }

  refreshList(): void {
    this.loadQualifications();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
