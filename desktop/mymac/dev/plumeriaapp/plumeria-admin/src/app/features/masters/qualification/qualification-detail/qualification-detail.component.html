<div class="container">
  <mat-card class="detail-card">
    <mat-card-header>
      <mat-card-title>Qualification Details</mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary" (click)="editQualification()" *ngIf="qualification">
          <mat-icon>edit</mat-icon> Edit
        </button>
        <button mat-raised-button color="warn" (click)="deleteQualification()" *ngIf="qualification">
          <mat-icon>delete</mat-icon> Delete
        </button>
        <button mat-raised-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back
        </button>
      </div>
    </mat-card-header>
    
    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>
    
    <div class="error-container" *ngIf="errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" (click)="goBack()">Go Back</button>
    </div>
    
    <mat-card-content *ngIf="!isLoading && !errorMessage && qualification">
      <div class="detail-section">
        <div class="status-badge-container">
          <span class="status-badge" [ngClass]="{ 'active-status': qualification.is_active, 'inactive-status': !qualification.is_active }">
            {{ qualification.is_active ? 'Active' : 'Inactive' }}
          </span>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">ID:</div>
          <div class="detail-value">{{ qualification.id }}</div>
        </div>
        
        <div class="detail-row">
          <div class="detail-label">Name:</div>
          <div class="detail-value">{{ qualification.name }}</div>
        </div>
        
        <mat-divider></mat-divider>
        
        <div class="detail-row">
          <div class="detail-label">Created By:</div>
          <div class="detail-value">{{ qualification.created_by_username || 'N/A' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="qualification.created_at">
          <div class="detail-label">Created At:</div>
          <div class="detail-value">{{ qualification.created_at | date:'medium' }}</div>
        </div>
        
        <div class="detail-row" *ngIf="qualification.updated_at">
          <div class="detail-label">Last Updated:</div>
          <div class="detail-value">{{ qualification.updated_at | date:'medium' }}</div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>