<div class="blood-group-list-container">
  <div class="list-header">
    <h1>Blood Groups</h1>
    <button mat-raised-button color="primary" routerLink="new">
      <mat-icon>add</mat-icon> Add Blood Group
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Search field -->
      <div class="filter-container">
        <div class="search-field">
          <mat-form-field appearance="outline">
            <mat-label>Search Blood Groups</mat-label>
            <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name or creator">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="action-buttons">
        <div class="left-actions">
          <button mat-raised-button color="warn" (click)="bulkDeleteSelected()"
                  [disabled]="selection.selected.length === 0"
                  matTooltip="Delete selected blood groups">
            <mat-icon>delete</mat-icon> Delete Selected ({{ selection.selected.length }})
          </button>

          <mat-slide-toggle
            [(ngModel)]="includeInactive"
            (change)="toggleIncludeInactive()"
            matTooltip="Include inactive blood groups"
            class="show-inactive-toggle">
            Show Inactive
          </mat-slide-toggle>

          <button mat-icon-button (click)="refreshList()" matTooltip="Refresh list">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </div>

      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading blood groups...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Success message -->
      <div class="success-message" *ngIf="successMessage">
        <mat-icon>check_circle</mat-icon> {{ successMessage }}
      </div>

      <!-- No data message -->
      <div class="no-data-message" *ngIf="!isLoading && filteredBloodGroups.length === 0">
        <mat-icon>info</mat-icon>
        <p>No blood groups found. Try adjusting your search or filters.</p>
      </div>

      <!-- Data table -->
      <div class="table-container mat-elevation-z2" *ngIf="!isLoading && filteredBloodGroups.length > 0">
        <table mat-table [dataSource]="displayedBloodGroups" matSort>
          <!-- Checkbox Column -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox
                (change)="$event ? toggleAllRows() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let bloodGroup">
              <mat-checkbox
                (click)="$event.stopPropagation()"
                (change)="$event ? selection.toggle(bloodGroup) : null"
                [checked]="selection.isSelected(bloodGroup)">
              </mat-checkbox>
            </td>
          </ng-container>

          <!-- ID Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> ID </th>
            <td mat-cell *matCellDef="let bloodGroup"> {{bloodGroup.id}} </td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
            <td mat-cell *matCellDef="let bloodGroup"> {{bloodGroup.name}} </td>
          </ng-container>

          <!-- Code Column removed -->

          <!-- Created By Column -->
          <ng-container matColumnDef="created_by">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Created By </th>
            <td mat-cell *matCellDef="let bloodGroup"> {{bloodGroup.created_by_username || 'N/A'}} </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
            <td mat-cell *matCellDef="let bloodGroup">
              <span [ngClass]="bloodGroup.is_active ? 'status-active' : 'status-inactive'">
                {{ bloodGroup.is_active ? 'Active' : 'Inactive' }}
              </span>

            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let bloodGroup">
               <button mat-icon-button (click)="toggleStatus(bloodGroup)"
                      [matTooltip]="bloodGroup.is_active ? 'Deactivate' : 'Activate'">
                <mat-icon>{{ bloodGroup.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="[bloodGroup.id]" matTooltip="View details">
                <mat-icon>visibility</mat-icon>
              </button>
              <button mat-icon-button [routerLink]="['edit', bloodGroup.id]" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button (click)="deleteBloodGroup(bloodGroup)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- Paginator -->
        <mat-paginator
          [length]="totalBloodGroups"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          (page)="onPageChange($event)"
          showFirstLastButtons>
        </mat-paginator>
      </div>
    </mat-card-content>
  </mat-card>
</div>
