import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

// Define a simple ProjectType interface
interface ProjectType {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

@Component({
  selector: 'app-project-type-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule,
  ],
  templateUrl: './project-type-detail.component.html',
  styleUrls: ['./project-type-detail.component.scss'],
})
export class ProjectTypeDetailComponent implements OnInit {
  projectType: ProjectType | null = null;
  isLoading = false;
  errorMessage = '';
  projectTypeId: number | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.projectTypeId = +id;
        this.loadProjectType(this.projectTypeId);
      } else {
        this.errorMessage = 'Project Type ID not provided';
        this.isLoading = false;
      }
    });
  }

  loadProjectType(id: number): void {
    this.isLoading = true;

    // Placeholder for API call
    // This would be replaced with actual service call
    setTimeout(() => {
      this.projectType = {
        id: id,
        name: 'Web Development',
        description: 'Projects related to web development',
        is_active: true,
        created_by_username: 'admin',
        created_at: '2023-05-17T10:00:00Z',
        updated_at: '2023-05-17T10:00:00Z',
      };
      this.isLoading = false;
    }, 500);
  }

  editProjectType(): void {
    if (this.projectTypeId) {
      this.router.navigate(['../edit', this.projectTypeId], {
        relativeTo: this.route,
      });
    }
  }

  deleteProjectType(): void {
    if (!this.projectType) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the project type "${this.projectType.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Placeholder for API call
        this.showSnackBar(
          `Project Type ${this.projectType?.name} deleted successfully`
        );
        this.router.navigate(['../../'], { relativeTo: this.route });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
