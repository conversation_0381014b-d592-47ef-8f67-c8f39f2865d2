import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatChipsModule } from '@angular/material/chips';
import { SelectionModel } from '@angular/cdk/collections';
import {
  BrandService,
  Brand,
} from '../../../../core/services/masters/brand.service';

@Component({
  selector: 'app-brand-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatSlideToggleModule,
    MatPaginatorModule,
    MatSortModule,
    MatChipsModule,
  ],
  templateUrl: './brand-list.component.html',
  styleUrls: ['./brand-list.component.scss'],
})
export class BrandListComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'product_subcategory_name',
    'product_category_name',
    'created_by',
    'status',
    'actions',
  ];
  displayedBrands = new MatTableDataSource<Brand>([]);
  selection = new SelectionModel<Brand>(true, []);

  searchTerm = '';
  includeInactive = false;
  isLoading = false;
  errorMessage = '';

  // Pagination
  totalBrands = 0;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];

  constructor(
    private brandService: BrandService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadBrands();
  }

  loadBrands(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.brandService.getAllBrands(this.includeInactive).subscribe({
      next: (brands) => {
        this.displayedBrands.data = brands;
        this.totalBrands = brands.length;
        this.applyFilter();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load brands: ${error.message}`;
        this.isLoading = false;
        this.snackBar.open(this.errorMessage, 'Close', { duration: 5000 });
      },
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.trim().toLowerCase();
    this.displayedBrands.filter = filterValue;

    this.displayedBrands.filterPredicate = (data: Brand, filter: string) => {
      const searchStr = (
        data.name +
        data.product_subcategory_name +
        data.product_category_name +
        data.created_by_username
      ).toLowerCase();
      return searchStr.includes(filter);
    };

    if (this.displayedBrands.paginator) {
      this.displayedBrands.paginator.firstPage();
    }
  }

  toggleIncludeInactive(): void {
    this.loadBrands();
  }

  refreshList(): void {
    this.selection.clear();
    this.loadBrands();
  }

  // Selection methods
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedBrands.data.length;
    return numSelected === numRows;
  }

  masterToggle(): void {
    this.isAllSelected()
      ? this.selection.clear()
      : this.displayedBrands.data.forEach((row) => this.selection.select(row));
  }

  // Status toggle
  toggleBrandStatus(brand: Brand): void {
    this.brandService.toggleBrandStatus(brand.id!).subscribe({
      next: (updatedBrand) => {
        const index = this.displayedBrands.data.findIndex(
          (b) => b.id === brand.id
        );
        if (index !== -1) {
          this.displayedBrands.data[index] = updatedBrand;
          this.displayedBrands._updateChangeSubscription();
        }
        this.snackBar.open(
          `Brand ${
            updatedBrand.is_active ? 'activated' : 'deactivated'
          } successfully`,
          'Close',
          { duration: 3000 }
        );
      },
      error: (error) => {
        this.snackBar.open(
          `Failed to update brand status: ${error.message}`,
          'Close',
          {
            duration: 5000,
          }
        );
      },
    });
  }

  // Delete single brand
  deleteBrand(brand: Brand): void {
    if (confirm(`Are you sure you want to delete the brand "${brand.name}"?`)) {
      this.brandService.deleteBrand(brand.id!).subscribe({
        next: () => {
          this.snackBar.open('Brand deleted successfully', 'Close', {
            duration: 3000,
          });
          this.loadBrands();
        },
        error: (error) => {
          this.snackBar.open(
            `Failed to delete brand: ${error.message}`,
            'Close',
            {
              duration: 5000,
            }
          );
        },
      });
    }
  }

  // Bulk delete selected brands
  bulkDeleteSelected(): void {
    const selectedBrands = this.selection.selected;
    if (selectedBrands.length === 0) return;

    const brandNames = selectedBrands.map((b) => b.name).join(', ');
    if (
      confirm(
        `Are you sure you want to delete ${selectedBrands.length} selected brand(s): ${brandNames}?`
      )
    ) {
      let deletedCount = 0;
      let errorCount = 0;

      selectedBrands.forEach((brand) => {
        this.brandService.deleteBrand(brand.id!).subscribe({
          next: () => {
            deletedCount++;
            if (deletedCount + errorCount === selectedBrands.length) {
              this.handleBulkDeleteComplete(deletedCount, errorCount);
            }
          },
          error: () => {
            errorCount++;
            if (deletedCount + errorCount === selectedBrands.length) {
              this.handleBulkDeleteComplete(deletedCount, errorCount);
            }
          },
        });
      });
    }
  }

  private handleBulkDeleteComplete(
    deletedCount: number,
    errorCount: number
  ): void {
    this.selection.clear();
    this.loadBrands();

    if (errorCount === 0) {
      this.snackBar.open(
        `${deletedCount} brand(s) deleted successfully`,
        'Close',
        {
          duration: 3000,
        }
      );
    } else {
      this.snackBar.open(
        `${deletedCount} brand(s) deleted, ${errorCount} failed`,
        'Close',
        { duration: 5000 }
      );
    }
  }

  // Pagination
  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    // Handle pagination if needed
  }
}
