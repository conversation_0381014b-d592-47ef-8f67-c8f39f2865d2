<div class="container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit' : 'Create' }} Product Subcategory</mat-card-title>
    </mat-card-header>

    <div class="loading-shade" *ngIf="isLoading">
      <mat-spinner diameter="50"></mat-spinner>
    </div>

    <div class="error-container" *ngIf="!isLoading && errorMessage">
      <p class="error-message">{{ errorMessage }}</p>
      <button mat-raised-button color="primary" routerLink="..">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
    </div>

    <mat-card-content *ngIf="!isLoading && !errorMessage">
      <form [formGroup]="productSubcategoryForm" (ngSubmit)="onSubmit()">
        <div class="form-field">
          <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter product subcategory name" required>
            <mat-error *ngIf="productSubcategoryForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
            <mat-error *ngIf="productSubcategoryForm.get('name')?.hasError('maxlength')">
              Name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-field">
          <mat-form-field appearance="outline">
            <mat-label>Product Category</mat-label>
            <mat-select formControlName="category_id" required>
              <mat-option *ngFor="let category of productCategories" [value]="category.id">
                {{ category.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="productSubcategoryForm.get('category_id')?.hasError('required')">
              Product category is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-field">
          <mat-form-field appearance="outline">
            <mat-label>Description</mat-label>
            <textarea matInput formControlName="description" placeholder="Enter description" rows="3"></textarea>
            <mat-error *ngIf="productSubcategoryForm.get('description')?.hasError('maxlength')">
              Description cannot exceed 255 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-field toggle-field">
          <mat-slide-toggle formControlName="is_active" color="primary">
            Active
          </mat-slide-toggle>
        </div>

        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit" [disabled]="productSubcategoryForm.invalid || isSubmitting">
            {{ isSubmitting ? 'Saving...' : 'Save' }}
          </button>
          <button mat-button type="button" (click)="cancel()">
            Cancel
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
