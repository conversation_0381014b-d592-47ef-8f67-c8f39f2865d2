import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { DesignationService } from '../../../../core/services/masters/designation.service';
import { Designation } from '../../../../core/models/masters/designation';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-designation-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatTooltipModule,
    MatDialogModule,
    MatSnackBarModule,
  ],
  templateUrl: './designation-detail.component.html',
  styleUrls: ['./designation-detail.component.scss'],
})
export class DesignationDetailComponent implements OnInit {
  designation: Designation | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private designationService: DesignationService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.loadDesignation(+id);
      } else {
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  loadDesignation(id: number): void {
    this.isLoading = true;
    this.designationService.getDesignationById(id).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          this.designation = response.data;
        } else {
          this.errorMessage =
            response.message || 'Failed to load designation details';
          this.showSnackBar(this.errorMessage, true);
          this.router.navigate(['../'], { relativeTo: this.route });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading designation: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
        this.router.navigate(['../'], { relativeTo: this.route });
      },
    });
  }

  deleteDesignation(): void {
    if (!this.designation) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the designation "${this.designation.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.designation) {
        this.designationService
          .deleteDesignation(this.designation.id)
          .subscribe({
            next: (response) => {
              if (response.success) {
                this.showSnackBar('Designation deleted successfully');
                this.router.navigate(['../../'], { relativeTo: this.route });
              } else {
                this.errorMessage =
                  response.message || 'Failed to delete designation';
                this.showSnackBar(this.errorMessage, true);
              }
            },
            error: (error) => {
              this.errorMessage =
                'Error deleting designation: ' + this.getErrorMessage(error);
              this.showSnackBar(this.errorMessage, true);
            },
          });
      }
    });
  }

  toggleStatus(): void {
    if (!this.designation) return;

    const newStatus = !this.designation.is_active;

    this.designationService
      .toggleDesignationStatus(this.designation.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.designation!.is_active = newStatus;
            this.showSnackBar(
              `Designation ${
                newStatus ? 'activated' : 'deactivated'
              } successfully`
            );
          } else {
            this.errorMessage =
              response.message || 'Failed to update designation status';
            this.showSnackBar(this.errorMessage, true);
          }
        },
        error: (error) => {
          this.errorMessage =
            'Error updating designation status: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
        },
      });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getStatusClass(): string {
    return this.designation?.is_active ? 'status-active' : 'status-inactive';
  }

  getStatusText(): string {
    return this.designation?.is_active ? 'Active' : 'Inactive';
  }

  getFormattedDate(dateString?: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  }
}
