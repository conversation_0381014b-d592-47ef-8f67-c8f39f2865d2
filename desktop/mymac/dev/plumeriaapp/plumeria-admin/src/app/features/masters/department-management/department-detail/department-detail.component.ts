import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { DepartmentService } from '../../../../core/services/masters/department.service';
import { Department } from '../../../../core/models/masters/department';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-department-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatTooltipModule,
    MatDialogModule,
    MatSnackBarModule,
  ],
  templateUrl: './department-detail.component.html',
  styleUrls: ['./department-detail.component.scss'],
})
export class DepartmentDetailComponent implements OnInit {
  department: Department | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private departmentService: DepartmentService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadDepartment(+id);
      } else {
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  loadDepartment(id: number): void {
    this.isLoading = true;
    this.departmentService.getDepartmentById(id).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          this.department = response.data;
        } else {
          this.errorMessage = response.message || 'Failed to load department details';
          this.showSnackBar(this.errorMessage, true);
          this.router.navigate(['../'], { relativeTo: this.route });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading department: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  deleteDepartment(): void {
    if (!this.department) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the department "${this.department.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.department) {
        this.departmentService.deleteDepartment(this.department.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar('Department deleted successfully');
              this.router.navigate(['../../'], { relativeTo: this.route });
            } else {
              this.errorMessage = response.message || 'Failed to delete department';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage = 'Error deleting department: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
          }
        });
      }
    });
  }

  toggleStatus(): void {
    if (!this.department) return;

    const newStatus = !this.department.is_active;
    
    this.departmentService.toggleDepartmentStatus(this.department.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          this.department!.is_active = newStatus;
          this.showSnackBar(`Department ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.errorMessage = response.message || 'Failed to update department status';
          this.showSnackBar(this.errorMessage, true);
        }
      },
      error: (error) => {
        this.errorMessage = 'Error updating department status: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
      }
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getStatusClass(): string {
    return this.department?.is_active ? 'status-active' : 'status-inactive';
  }

  getStatusText(): string {
    return this.department?.is_active ? 'Active' : 'Inactive';
  }

  getFormattedDate(dateString?: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  }
}
