.container {
  padding: 20px;
}

.list-card {
  width: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

mat-form-field {
  width: 50%;
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.error-message {
  color: #f44336;
  margin-bottom: 15px;
}

.table-container {
  position: relative;
  min-height: 200px;
  overflow: auto;
}

table {
  width: 100%;
}

.mat-column-select {
  width: 60px;
  padding-right: 8px;
}

.mat-column-id {
  width: 80px;
}

.mat-column-actions {
  width: 160px;
  text-align: center;
}

tr.mat-row:hover {
  background-color: #f5f5f5;
}

.mat-cell {
  padding: 8px 16px;
}

/* Status badge styling */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.active-status {
  background-color: #e6f4ea;
  color: #137333;
}

.inactive-status {
  background-color: #fce8e6;
  color: #c5221f;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  mat-form-field {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .header-actions {
    flex-wrap: wrap;
  }
}