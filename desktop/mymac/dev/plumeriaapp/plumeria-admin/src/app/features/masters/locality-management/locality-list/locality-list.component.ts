import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { SelectionModel } from '@angular/cdk/collections';

import { LocalityService } from '../../../../core/services/masters/locality.service';
import { CityService } from '../../../../core/services/masters/city.service';
import { StateService } from '../../../../core/services/masters/state.service';
import { CountryService } from '../../../../core/services/masters/country.service';
import { Locality } from '../../../../core/models/masters/locality';
import { City } from '../../../../core/models/masters/city';
import { State } from '../../../../core/models/masters/state';
import { Country } from '../../../../core/models/masters/country';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-locality-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
    MatSelectModule,
  ],
  templateUrl: './locality-list.component.html',
  styleUrls: ['./locality-list.component.scss'],
})
export class LocalityListComponent implements OnInit {
  localities: Locality[] = [];
  filteredLocalities: Locality[] = [];
  displayedLocalities: Locality[] = []; // Localities after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'city_name',
    'state_name',
    'country_name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<Locality>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;

  // Filters
  cities: City[] = [];
  states: State[] = [];
  countries: Country[] = [];
  selectedCityId: number | null = null;
  selectedStateId: number | null = null;
  selectedCountryId: number | null = null;
  loadingCities = false;
  loadingStates = false;
  loadingCountries = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalLocalities = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private localityService: LocalityService,
    private cityService: CityService,
    private stateService: StateService,
    private countryService: CountryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadCountries();
    this.loadLocalities();
  }

  loadCountries(): void {
    this.loadingCountries = true;

    this.countryService.getCountries(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingCountries = false;
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        this.loadingCountries = false;
      },
    });
  }

  loadStates(countryId?: number): void {
    this.loadingStates = true;
    this.states = [];

    this.stateService.getStates(true, countryId).subscribe({
      next: (response) => {
        if (response.success) {
          this.states = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingStates = false;
      },
      error: (error) => {
        console.error('Error loading states:', error);
        this.loadingStates = false;
      },
    });
  }

  loadCities(stateId?: number): void {
    this.loadingCities = true;
    this.cities = [];

    this.cityService.getCities(true, stateId).subscribe({
      next: (response) => {
        if (response.success) {
          this.cities = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingCities = false;
      },
      error: (error) => {
        console.error('Error loading cities:', error);
        this.loadingCities = false;
      },
    });
  }

  loadLocalities(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.localityService
      .getLocalities(
        this.includeInactive,
        this.selectedCityId || undefined,
        this.selectedStateId || undefined
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.localities = Array.isArray(response.data) ? response.data : [];
            this.totalLocalities = this.localities.length;
            this.applyFilter();
          } else {
            this.errorMessage = response.message || 'Failed to load localities';
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage =
            'Error loading localities: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error loading localities:', error);
        },
      });
  }

  applyFilter(): void {
    // Filter localities based on search term
    const searchTermLower = this.searchTerm.toLowerCase().trim();

    this.filteredLocalities = this.localities.filter((locality) => {
      return (
        locality.name.toLowerCase().includes(searchTermLower) ||
        (locality.city_name &&
          locality.city_name.toLowerCase().includes(searchTermLower)) ||
        (locality.state_name &&
          locality.state_name.toLowerCase().includes(searchTermLower)) ||
        (locality.country_name &&
          locality.country_name.toLowerCase().includes(searchTermLower))
      );
    });

    this.totalLocalities = this.filteredLocalities.length;
    this.selection.clear();
    this.updateDisplayedLocalities();
  }

  updateDisplayedLocalities(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedLocalities = this.filteredLocalities.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedLocalities();
  }

  deleteLocality(locality: Locality): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the locality "${locality.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.localityService.deleteLocality(locality.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.successMessage = 'Locality deleted successfully';
              this.showSnackBar(this.successMessage);
              this.loadLocalities();
            } else {
              this.errorMessage =
                response.message || 'Failed to delete locality';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage =
              'Error deleting locality: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            console.error('Error deleting locality:', error);
          },
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    if (this.selection.selected.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${this.selection.selected.length} selected localities?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((locality) => locality.id);

        this.localityService.bulkDeleteLocalities(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.successMessage = 'Localities deleted successfully';
              this.showSnackBar(this.successMessage);
              this.selection.clear();
              this.loadLocalities();
            } else {
              this.errorMessage =
                response.message || 'Failed to delete localities';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage =
              'Error deleting localities: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            console.error('Error deleting localities:', error);
          },
        });
      }
    });
  }

  toggleStatus(locality: Locality): void {
    const newStatus = !locality.is_active;

    this.localityService
      .toggleLocalityStatus(locality.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success) {
            locality.is_active = newStatus;
            this.successMessage = `Locality ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`;
            this.showSnackBar(this.successMessage);
          } else {
            this.errorMessage =
              response.message || 'Failed to update locality status';
            this.showSnackBar(this.errorMessage, true);
          }
        },
        error: (error) => {
          this.errorMessage =
            'Error updating locality status: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
          console.error('Error updating locality status:', error);
        },
      });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedLocalities.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedLocalities);
    }
  }

  refreshList(): void {
    this.loadLocalities();
  }

  toggleIncludeInactive(): void {
    this.loadLocalities();
  }

  onCountryChange(): void {
    if (this.selectedCountryId) {
      this.loadStates(this.selectedCountryId);
      this.selectedStateId = null;
      this.selectedCityId = null;
      this.cities = [];
    } else {
      this.states = [];
      this.cities = [];
      this.selectedStateId = null;
      this.selectedCityId = null;
    }
    this.loadLocalities();
  }

  onStateChange(): void {
    if (this.selectedStateId) {
      this.loadCities(this.selectedStateId);
      this.selectedCityId = null;
    } else {
      this.cities = [];
      this.selectedCityId = null;
    }
    this.loadLocalities();
  }

  onCityChange(): void {
    this.loadLocalities();
  }

  clearCountryFilter(): void {
    this.selectedCountryId = null;
    this.states = [];
    this.cities = [];
    this.selectedStateId = null;
    this.selectedCityId = null;
    this.loadLocalities();
  }

  clearStateFilter(): void {
    this.selectedStateId = null;
    this.cities = [];
    this.selectedCityId = null;
    this.loadLocalities();
  }

  clearCityFilter(): void {
    this.selectedCityId = null;
    this.loadLocalities();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
