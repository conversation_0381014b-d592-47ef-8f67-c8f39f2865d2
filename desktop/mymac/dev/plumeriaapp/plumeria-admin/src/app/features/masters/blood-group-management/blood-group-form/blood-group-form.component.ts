import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';

import {
  BloodGroupService,
  BloodGroup,
} from '../../../../core/services/masters/blood-group.service';

@Component({
  selector: 'app-blood-group-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
  ],
  templateUrl: './blood-group-form.component.html',
  styleUrls: ['./blood-group-form.component.scss'],
})
export class BloodGroupFormComponent implements OnInit {
  bloodGroupForm!: FormGroup;
  isEditMode = false;
  bloodGroupId: number | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private bloodGroupService: BloodGroupService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();

    this.route.paramMap.subscribe((params) => {
      const idParam = params.get('id');
      if (idParam) {
        this.isEditMode = true;
        this.bloodGroupId = +idParam;
        this.loadBloodGroupData(this.bloodGroupId);
      }
    });
  }

  initForm(): void {
    this.bloodGroupForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100),
        ],
      ],
      is_active: [true],
    });
  }

  loadBloodGroupData(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.bloodGroupService.getBloodGroupById(id).subscribe({
      next: (response) => {
        if (response.success) {
          const bloodGroup = response.data as BloodGroup;
          this.bloodGroupForm.patchValue({
            name: bloodGroup.name,
            is_active: bloodGroup.is_active,
          });
        } else {
          this.errorMessage =
            response.message || 'Failed to load blood group data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading blood group data: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading blood group data:', error);
      },
    });
  }

  onSubmit(): void {
    if (this.bloodGroupForm.invalid) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const bloodGroupData = this.bloodGroupForm.value;

    if (this.isEditMode && this.bloodGroupId) {
      this.bloodGroupService
        .updateBloodGroup(this.bloodGroupId, bloodGroupData)
        .subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar('Blood group updated successfully');
              this.router.navigate(['../../', this.bloodGroupId], {
                relativeTo: this.route,
              });
            } else {
              this.errorMessage =
                response.message || 'Failed to update blood group';
              this.isLoading = false;
            }
          },
          error: (error) => {
            this.errorMessage =
              'Error updating blood group: ' + this.getErrorMessage(error);
            this.isLoading = false;
            console.error('Error updating blood group:', error);
          },
        });
    } else {
      this.bloodGroupService.createBloodGroup(bloodGroupData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Blood group created successfully');
            this.router.navigate(['..'], { relativeTo: this.route });
          } else {
            this.errorMessage =
              response.message || 'Failed to create blood group';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage =
            'Error creating blood group: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error creating blood group:', error);
        },
      });
    }
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }

  // Getters for form controls
  get nameControl(): FormControl {
    return this.bloodGroupForm.get('name') as FormControl;
  }

  // Code control removed
}
