import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {
  MeasurementUnitService,
  MeasurementUnit,
} from '../../../../core/services/measurement-unit.service';

@Component({
  selector: 'app-measurement-unit-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
  ],
  templateUrl: './measurement-unit-detail.component.html',
  styleUrls: ['./measurement-unit-detail.component.scss'],
})
export class MeasurementUnitDetailComponent implements OnInit {
  measurementUnit: MeasurementUnit | null = null;
  loading = false;
  measurementUnitId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private measurementUnitService: MeasurementUnitService,
    private snackBar: MatSnackBar
  ) {
    this.measurementUnitId = +this.route.snapshot.params['id'];
  }

  ngOnInit(): void {
    this.loadMeasurementUnit();
  }

  loadMeasurementUnit(): void {
    this.loading = true;
    this.measurementUnitService.getMeasurementUnitById(this.measurementUnitId).subscribe({
      next: (response) => {
        if (response.success) {
          this.measurementUnit = response.data;
        } else {
          this.showSnackBar('Measurement unit not found', true);
          this.router.navigate(['/admin/masters/measurement-units']);
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading measurement unit:', error);
        this.showSnackBar('Failed to load measurement unit', true);
        this.loading = false;
      },
    });
  }

  navigateToEdit(): void {
    this.router.navigate(['/admin/masters/measurement-units/edit', this.measurementUnitId]);
  }

  navigateToList(): void {
    this.router.navigate(['/admin/masters/measurement-units']);
  }

  toggleStatus(): void {
    if (!this.measurementUnit) return;

    const newStatus = !this.measurementUnit.is_active;
    this.measurementUnitService
      .toggleMeasurementUnitStatus(this.measurementUnit.id, newStatus)
      .subscribe({
        next: (response) => {
          if (response.success && this.measurementUnit) {
            this.measurementUnit.is_active = newStatus;
            this.showSnackBar(
              `Measurement unit ${newStatus ? 'activated' : 'deactivated'} successfully`
            );
          }
        },
        error: (error) => {
          console.error('Error toggling measurement unit status:', error);
          this.showSnackBar('Failed to update measurement unit status', true);
        },
      });
  }

  deleteMeasurementUnit(): void {
    if (!this.measurementUnit) return;

    const confirmMessage = `Are you sure you want to delete measurement unit "${this.measurementUnit.name}"?`;
    if (confirm(confirmMessage)) {
      this.measurementUnitService.deleteMeasurementUnit(this.measurementUnit.id).subscribe({
        next: (response) => {
          this.showSnackBar('Measurement unit deleted successfully');
          this.router.navigate(['/admin/masters/measurement-units']);
        },
        error: (error) => {
          console.error('Error deleting measurement unit:', error);
          this.showSnackBar('Failed to delete measurement unit', true);
        },
      });
    }
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
