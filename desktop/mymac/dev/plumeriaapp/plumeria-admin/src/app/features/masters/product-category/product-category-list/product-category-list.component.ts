import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import {
  ProductCategoryService,
  ProductCategory,
} from '../../../../core/services/masters/product-category.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-product-category-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './product-category-list.component.html',
  styleUrl: './product-category-list.component.scss',
})
export class ProductCategoryListComponent implements OnInit {
  productCategories: ProductCategory[] = [];
  filteredProductCategories: ProductCategory[] = [];
  displayedProductCategories: ProductCategory[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'description',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<ProductCategory>(true, []);
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = true; // Set to true to include inactive by default

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalProductCategories = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private productCategoryService: ProductCategoryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProductCategories();
  }

  loadProductCategories(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.productCategoryService
      .getAllProductCategories(this.includeInactive)
      .subscribe({
        next: (productCategories) => {
          this.productCategories = productCategories;
          this.totalProductCategories = productCategories.length;
          this.applyFilter();
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = `Failed to load product categories: ${error.message}`;
          this.isLoading = false;
        },
      });
  }

  toggleIncludeInactive(): void {
    // Reload product categories when the toggle changes
    this.loadProductCategories();
  }

  applyFilter(): void {
    if (!this.searchTerm) {
      this.filteredProductCategories = [...this.productCategories];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase();
      this.filteredProductCategories = this.productCategories.filter(
        (productCategory) =>
          productCategory.name.toLowerCase().includes(searchTermLower) ||
          (productCategory.description &&
            productCategory.description
              .toLowerCase()
              .includes(searchTermLower)) ||
          (productCategory.created_by_username &&
            productCategory.created_by_username
              .toLowerCase()
              .includes(searchTermLower))
      );
    }

    this.totalProductCategories = this.filteredProductCategories.length;
    this.updateDisplayedProductCategories();
  }

  updateDisplayedProductCategories(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedProductCategories = this.filteredProductCategories.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedProductCategories();
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedProductCategories.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedProductCategories);
    }
  }

  refreshList(): void {
    this.selection.clear();
    this.searchTerm = '';
    this.loadProductCategories();
  }

  toggleProductCategoryStatus(productCategory: ProductCategory): void {
    const newStatus = !productCategory.is_active;

    this.productCategoryService
      .toggleProductCategoryStatus(productCategory.id!, newStatus)
      .subscribe({
        next: (updatedProductCategory) => {
          // Update the product category in the list
          const index = this.productCategories.findIndex(
            (pc) => pc.id === updatedProductCategory.id
          );
          if (index !== -1) {
            this.productCategories[index] = updatedProductCategory;
            this.applyFilter();
          }

          this.snackBar.open(
            `Product category ${updatedProductCategory.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`,
            'Close',
            { duration: 3000 }
          );
        },
        error: (error) => {
          this.snackBar.open(
            `Failed to update status: ${this.getErrorMessage(error)}`,
            'Close',
            { duration: 5000 }
          );
        },
      });
  }

  deleteProductCategory(productCategory: ProductCategory): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the product category "${productCategory.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.productCategoryService
          .deleteProductCategory(productCategory.id!)
          .subscribe({
            next: (success) => {
              if (success) {
                // Remove the deleted product category from the list
                this.productCategories = this.productCategories.filter(
                  (pc) => pc.id !== productCategory.id
                );
                this.selection.deselect(productCategory);
                this.applyFilter();

                this.snackBar.open(
                  `Product category ${productCategory.name} deleted successfully`,
                  'Close',
                  { duration: 3000 }
                );
              }
            },
            error: (error) => {
              this.snackBar.open(
                `Failed to delete product category: ${this.getErrorMessage(
                  error
                )}`,
                'Close',
                { duration: 5000 }
              );
            },
          });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedProductCategories = this.selection.selected;
    if (selectedProductCategories.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedProductCategories.length} selected product category(s)?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedProductCategories.map(
          (productCategory) => productCategory.id!
        );

        this.productCategoryService.bulkDeleteProductCategories(ids).subscribe({
          next: (success) => {
            if (success) {
              // Remove the deleted product categories from the list
              this.productCategories = this.productCategories.filter(
                (productCategory) => !ids.includes(productCategory.id!)
              );
              this.selection.clear();
              this.applyFilter();

              this.snackBar.open(
                `${ids.length} product category(s) deleted successfully`,
                'Close',
                { duration: 3000 }
              );
            }
          },
          error: (error) => {
            this.snackBar.open(
              `Failed to delete product categories: ${this.getErrorMessage(
                error
              )}`,
              'Close',
              { duration: 5000 }
            );
          },
        });
      }
    });
  }

  getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
