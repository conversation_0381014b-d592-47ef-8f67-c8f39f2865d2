import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginator,
  PageEvent,
} from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';
import {
  ProjectTypeService,
  ProjectType,
} from '../../../../core/services/masters/project-type.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-project-type-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './project-type-list.component.html',
  styleUrls: ['./project-type-list.component.scss'],
})
export class ProjectTypeListComponent implements OnInit {
  projectTypes: ProjectType[] = [];
  filteredProjectTypes: ProjectType[] = [];
  displayedProjectTypes: ProjectType[] = [];
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<ProjectType>(true, []);
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = true; // Set to true to include inactive by default

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalProjectTypes = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private projectTypeService: ProjectTypeService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadProjectTypes();
  }

  loadProjectTypes(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.projectTypeService.getAllProjectTypes(this.includeInactive).subscribe({
      next: (projectTypes) => {
        this.projectTypes = projectTypes;
        this.totalProjectTypes = projectTypes.length;
        this.applyFilter();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load project types: ${error.message}`;
        this.isLoading = false;
      },
    });
  }

  toggleIncludeInactive(): void {
    // Reload project types when the toggle changes
    this.loadProjectTypes();
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredProjectTypes = this.projectTypes.filter(
      (type) =>
        type.name.toLowerCase().includes(filterValue) ||
        (type.created_by_username &&
          type.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalProjectTypes = this.filteredProjectTypes.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedProjectTypes();
  }

  updateDisplayedProjectTypes(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedProjectTypes = this.filteredProjectTypes.slice(
      startIndex,
      endIndex
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedProjectTypes();
  }

  toggleStatus(projectType: ProjectType): void {
    const newStatus = !projectType.is_active;

    this.projectTypeService
      .toggleProjectTypeStatus(projectType.id!, newStatus)
      .subscribe({
        next: (updatedType) => {
          projectType.is_active = updatedType.is_active;
          this.showSnackBar(
            `Project Type ${projectType.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        },
        error: (error) => {
          this.showSnackBar(`Error: ${error.message}`, true);
          // Revert toggle in UI
          projectType.is_active = !newStatus;
        },
      });
  }

  deleteProjectType(projectType: ProjectType): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the project type "${projectType.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.projectTypeService.deleteProjectType(projectType.id!).subscribe({
          next: () => {
            this.projectTypes = this.projectTypes.filter(
              (c) => c.id !== projectType.id
            );
            this.applyFilter();
            this.showSnackBar(
              `Project Type ${projectType.name} deleted successfully`
            );
          },
          error: (error) => {
            this.showSnackBar(`Error: ${error.message}`, true);
          },
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedProjectTypes = this.selection.selected;

    if (selectedProjectTypes.length === 0) {
      this.showSnackBar('No project types selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedProjectTypes.length} selected project types?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedProjectTypes.map((type) => type.id!);

        this.projectTypeService.bulkDeleteProjectTypes(ids).subscribe({
          next: () => {
            this.projectTypes = this.projectTypes.filter(
              (type) => !ids.includes(type.id!)
            );
            this.selection.clear();
            this.applyFilter();
            this.showSnackBar(
              `Successfully deleted ${ids.length} project types`
            );
          },
          error: (error) => {
            this.showSnackBar(`Error: ${error.message}`, true);
          },
        });
      }
    });
  }

  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedProjectTypes.length;
    return numSelected === numRows && numRows > 0;
  }

  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedProjectTypes);
    }
  }

  refreshList(): void {
    this.loadProjectTypes();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
