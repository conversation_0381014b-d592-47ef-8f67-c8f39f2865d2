import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';

import { CountryService } from '../../../../core/services/masters/country.service';
import { Country } from '../../../../core/models/masters/country';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-country-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSnackBarModule
  ],
  templateUrl: './country-detail.component.html',
  styleUrls: ['./country-detail.component.scss']
})
export class CountryDetailComponent implements OnInit {
  country: Country | null = null;
  isLoading = true;
  errorMessage = '';
  countryId: number | null = null;

  constructor(
    private countryService: CountryService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.countryId = +params['id'];
        this.loadCountryData();
      } else {
        this.errorMessage = 'Country ID is required';
        this.isLoading = false;
      }
    });
  }

  loadCountryData(): void {
    if (!this.countryId) return;

    this.isLoading = true;
    this.errorMessage = '';

    this.countryService.getCountryById(this.countryId).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          this.country = response.data;
        } else {
          this.errorMessage = response.message || 'Failed to load country data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading country: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading country:', error);
      }
    });
  }

  deleteCountry(): void {
    if (!this.country) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the country "${this.country.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.countryId) {
        this.countryService.deleteCountry(this.countryId).subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar(`Country ${this.country?.name} deleted successfully`);
              this.router.navigate(['../../'], { relativeTo: this.route });
            } else {
              this.showSnackBar(`Failed to delete country: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting country: ${this.getErrorMessage(error)}`, true);
            console.error('Error deleting country:', error);
          }
        });
      }
    });
  }

  toggleStatus(): void {
    if (!this.country || !this.countryId) return;

    const newStatus = !this.country.is_active;
    
    this.countryService.toggleCountryStatus(this.countryId, newStatus).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          this.country!.is_active = newStatus;
          this.showSnackBar(`Country ${this.country?.name} ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.showSnackBar(`Failed to update status: ${response.message}`, true);
        }
      },
      error: (error) => {
        this.showSnackBar(`Error updating status: ${this.getErrorMessage(error)}`, true);
        console.error('Error toggling country status:', error);
      }
    });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }
}
