import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
  ProjectStatusService,
  ProjectStatus,
} from '../../../../core/services/masters/project-status.service';

@Component({
  selector: 'app-project-status-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    RouterModule,
  ],
  templateUrl: './project-status-form.component.html',
  styleUrls: ['./project-status-form.component.scss'],
})
export class ProjectStatusFormComponent implements OnInit {
  projectStatusForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  projectStatusId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private projectStatusService: ProjectStatusService
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.projectStatusId = +id;
        this.isEditMode = true;
        this.loadProjectStatus(this.projectStatusId);
      }
    });
  }

  initForm(): void {
    this.projectStatusForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      is_active: [true],
    });
  }

  loadProjectStatus(id: number): void {
    this.isLoading = true;
    this.projectStatusService.getProjectStatusById(id).subscribe({
      next: (projectStatus) => {
        this.projectStatusForm.patchValue(projectStatus);
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load project status: ${error.message}`;
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.projectStatusForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formData = this.projectStatusForm.value;

    if (this.isEditMode && this.projectStatusId) {
      // Update existing project status
      this.projectStatusService
        .updateProjectStatus(this.projectStatusId, formData)
        .subscribe({
          next: () => {
            this.isSubmitting = false;
            this.showSnackBar(`Project Status updated successfully`);
            this.router.navigate(['../../'], { relativeTo: this.route });
          },
          error: (error) => {
            this.isSubmitting = false;
            this.errorMessage = `Failed to update project status: ${error.message}`;
            this.showSnackBar(this.errorMessage, true);
          },
        });
    } else {
      // Create new project status
      this.projectStatusService.createProjectStatus(formData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.showSnackBar(`Project Status created successfully`);
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = `Failed to create project status: ${error.message}`;
          this.showSnackBar(this.errorMessage, true);
        },
      });
    }
  }

  onCancel(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
