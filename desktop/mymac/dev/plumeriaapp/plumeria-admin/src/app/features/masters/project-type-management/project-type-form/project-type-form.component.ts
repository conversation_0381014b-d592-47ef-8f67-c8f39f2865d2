import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ProjectTypeService, ProjectType } from '../../../../core/services/masters/project-type.service';

@Component({
  selector: 'app-project-type-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    RouterModule,
  ],
  templateUrl: './project-type-form.component.html',
  styleUrls: ['./project-type-form.component.scss'],
})
export class ProjectTypeFormComponent implements OnInit {
  projectTypeForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  isEditMode = false;
  projectTypeId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private projectTypeService: ProjectTypeService
  ) {}

  ngOnInit(): void {
    this.initForm();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.projectTypeId = +id;
        this.isEditMode = true;
        this.loadProjectType(this.projectTypeId);
      }
    });
  }

  initForm(): void {
    this.projectTypeForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      description: ['', Validators.maxLength(500)],
      is_active: [true],
    });
  }

  loadProjectType(id: number): void {
    this.isLoading = true;
    this.projectTypeService.getProjectTypeById(id).subscribe({
      next: (projectType) => {
        this.projectTypeForm.patchValue(projectType);
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = `Failed to load project type: ${error.message}`;
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.projectTypeForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    const formData = this.projectTypeForm.value;

    if (this.isEditMode && this.projectTypeId) {
      // Update existing project type
      this.projectTypeService.updateProjectType(this.projectTypeId, formData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.showSnackBar(`Project Type updated successfully`);
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = `Failed to update project type: ${error.message}`;
          this.showSnackBar(this.errorMessage, true);
        }
      });
    } else {
      // Create new project type
      this.projectTypeService.createProjectType(formData).subscribe({
        next: () => {
          this.isSubmitting = false;
          this.showSnackBar(`Project Type created successfully`);
          this.router.navigate(['../'], { relativeTo: this.route });
        },
        error: (error) => {
          this.isSubmitting = false;
          this.errorMessage = `Failed to create project type: ${error.message}`;
          this.showSnackBar(this.errorMessage, true);
        }
      });
    }
  }

  onCancel(): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
