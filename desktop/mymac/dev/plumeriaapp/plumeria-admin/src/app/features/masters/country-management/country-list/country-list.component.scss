@import 'styles/variables';
@import 'styles/mixins';

.country-list-container {
  @include card-container;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .filter-container {
    margin-bottom: $spacing-lg;
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;

    .search-field {
      flex: 1;
      min-width: 250px;

      mat-form-field {
        width: 100%;
      }
    }
  }

  .action-buttons {
    margin: $spacing-lg 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      .show-inactive-toggle {
        margin-left: $spacing-md;
      }
    }
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;

    p {
      margin-top: $spacing-md;
      color: $text-secondary;
    }
  }

  .error-message {
    @include message-box($error-color);
  }

  .success-message {
    @include message-box($success-color);
  }

  .no-data-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;
    color: $text-secondary;

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: $spacing-md;
      opacity: 0.5;
    }

    p {
      font-size: 16px;
    }
  }

  .table-container {
    @include table-container;
    background-color: $card-background;
    border-radius: $card-border-radius;
    overflow: hidden;

    .mat-column-select {
      width: 60px;
      padding-right: $spacing-sm;
    }

    .mat-column-id {
      width: 80px;
    }

    .mat-column-status {
      width: 150px;
    }

    .mat-column-actions {
      width: 200px;
      text-align: center;

      td {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        padding: 8px 16px;

        button {
          min-width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;

          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }

  mat-paginator {
    margin-top: 0;
  }

  // Status styling
  .status-active {
    color: #4caf50;
    font-weight: 600;
    background-color: rgba(76, 175, 80, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-transform: uppercase;
  }

  .status-inactive {
    color: #f44336;
    font-weight: 600;
    background-color: rgba(244, 67, 54, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    text-transform: uppercase;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .country-list-container {
    padding: $spacing-md;

    .list-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
    }

    .filter-container {
      mat-form-field {
        max-width: none;
      }
    }

    .action-buttons {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
    }

    .table-container {
      overflow-x: auto;

      .mat-column-actions {
        width: 160px;

        td {
          gap: 2px;
          padding: 4px 8px;

          button {
            min-width: 36px;
            height: 36px;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }
}
