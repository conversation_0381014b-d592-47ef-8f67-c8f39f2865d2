import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';

import { CountryService } from '../../../../core/services/masters/country.service';
import { Country } from '../../../../core/models/masters/country';

@Component({
  selector: 'app-country-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './country-form.component.html',
  styleUrls: ['./country-form.component.scss']
})
export class CountryFormComponent implements OnInit {
  countryForm!: FormGroup;
  isEditMode = false;
  countryId: number | null = null;
  isLoading = false;
  errorMessage = '';
  country: Country | null = null;

  constructor(
    private fb: FormBuilder,
    private countryService: CountryService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.createForm();

    // Check if we're in edit mode
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.countryId = +params['id'];
        this.isEditMode = true;
        this.loadCountryData();
      }
    });
  }

  createForm(): void {
    this.countryForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(10)]],
      is_active: [true]
    });
  }

  loadCountryData(): void {
    if (!this.countryId) return;

    this.isLoading = true;
    this.errorMessage = '';

    this.countryService.getCountryById(this.countryId).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          this.country = response.data;
          this.countryForm.patchValue({
            name: this.country.name,
            code: this.country.code,
            is_active: this.country.is_active
          });
        } else {
          this.errorMessage = 'Failed to load country data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading country: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading country:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.countryForm.invalid) {
      this.countryForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const countryData: Partial<Country> = this.countryForm.value;

    if (this.isEditMode && this.countryId) {
      // Update existing country
      this.countryService.updateCountry(this.countryId, countryData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Country updated successfully');
            this.router.navigate(['../..'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to update country';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage = 'Error updating country: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error updating country:', error);
        }
      });
    } else {
      // Create new country
      this.countryService.createCountry(countryData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Country created successfully');
            this.router.navigate(['..'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to create country';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage = 'Error creating country: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error creating country:', error);
        }
      });
    }
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }

  // Form validation helpers
  get nameControl() { return this.countryForm.get('name'); }
  get codeControl() { return this.countryForm.get('code'); }
}
