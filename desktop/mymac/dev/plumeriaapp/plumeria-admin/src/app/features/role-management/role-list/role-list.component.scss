// src/app/features/role-management/role-list/role-list.component.scss

.role-list-container {
    padding: 24px;
  
    .role-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
  
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
  
    .filter-container {
      margin-bottom: 24px;
  
      mat-form-field {
        width: 100%;
        max-width: 500px;
      }
    }
  
    .table-container {
      background-color: white;
      border-radius: 4px;
      overflow: hidden;
    }
  
    .role-table {
      width: 100%;
  
      .mat-column-id {
        width: 70px;
        padding-right: 24px;
      }
  
      .mat-column-actions {
        width: 80px;
        text-align: right;
      }
  
      .mat-column-status {
        width: 120px;
      }
    }
  
    .spinner-container, .error-container, .no-data-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      text-align: center;
    }
  
    .error-container {
      color: #f44336;
    }
  }