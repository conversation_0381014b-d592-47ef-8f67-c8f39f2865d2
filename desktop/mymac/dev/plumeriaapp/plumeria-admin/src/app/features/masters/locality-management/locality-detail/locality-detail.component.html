<div class="locality-detail-container">
  <div class="detail-header">
    <h1>Locality Details</h1>
    <div class="header-actions">
      <button mat-raised-button color="primary" routerLink="../">
        <mat-icon>arrow_back</mat-icon> Back to List
      </button>
      <button mat-raised-button color="accent" [routerLink]="['../edit', locality.id]" *ngIf="locality">
        <mat-icon>edit</mat-icon> Edit
      </button>
    </div>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading locality details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Locality details -->
      <div class="detail-content" *ngIf="locality && !isLoading">
        <div class="detail-header-section">
          <div class="detail-title">
            <h2>{{ locality.name }}</h2>
          </div>
          <div class="detail-status">
            <span [ngClass]="getStatusClass()">{{ getStatusText() }}</span>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-section">
          <h3>Location Information</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <div class="detail-label">City</div>
              <div class="detail-value">{{ locality.city_name || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">State</div>
              <div class="detail-value">{{ locality.state_name || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Country</div>
              <div class="detail-value">{{ locality.country_name || 'N/A' }}</div>
            </div>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-section">
          <h3>System Information</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <div class="detail-label">Created By</div>
              <div class="detail-value">{{ locality.created_by_username || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Created At</div>
              <div class="detail-value">{{ getFormattedDate(locality.created_at) }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Updated By</div>
              <div class="detail-value">{{ locality.updated_by_username || 'N/A' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Updated At</div>
              <div class="detail-value">{{ getFormattedDate(locality.updated_at) }}</div>
            </div>
          </div>
        </div>

        <div class="detail-actions">
          <button mat-raised-button color="primary" (click)="toggleStatus()" matTooltip="Change status">
            <mat-icon>{{ locality.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ locality.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="warn" (click)="deleteLocality()" matTooltip="Delete this locality">
            <mat-icon>delete</mat-icon> Delete
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
