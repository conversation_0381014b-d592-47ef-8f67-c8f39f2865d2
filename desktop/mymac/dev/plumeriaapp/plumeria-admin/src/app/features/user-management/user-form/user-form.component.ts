// src/app/features/user-management/user-form/user-form.component.ts

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { UserService } from '../../../core/services/user.service';
import { RoleService } from '../../../core/services/role.service';
import { Role } from '../../../core/models/user';

@Component({
  selector: 'app-user-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    RouterModule,
  ],
  templateUrl: './user-form.component.html',
  styleUrl: './user-form.component.scss',
})
export class UserFormComponent implements OnInit {
  userForm!: FormGroup;
  roles: Role[] = [];
  isEditMode = false;
  userId: number | null = null;
  isLoading = false;
  errorMessage = '';
  showPassword = false;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private roleService: RoleService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createForm();
    this.loadRoles();

    // Check if we're in edit mode
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.userId = +params['id'];
        this.isEditMode = true;
        this.loadUserData();
      }
    });
  }

  createForm(): void {
    this.userForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(4)]],
      email: ['', [Validators.required, Validators.email]],
      password: [
        '',
        this.isEditMode ? [] : [Validators.required, Validators.minLength(6)],
      ],
      name: ['', Validators.required],
      phone: [''],
      is_active: [true],
      roles: [[]], // Changed to array for multiple roles
    });
  }

  loadRoles(): void {
    this.roleService.getRoles(true).subscribe({
      next: (response) => {
        this.roles = response.data;
      },
      error: (error) => {
        this.errorMessage = 'Error loading roles: ' + error.message;
      },
    });
  }

  loadUserData(): void {
    if (!this.userId) return;

    this.isLoading = true;
    this.userService.getUserById(this.userId).subscribe({
      next: (response) => {
        const user = response.data;
        console.log('Loaded user data:', user);

        // Get all roles
        const selectedRoles = user.roles || [];
        console.log('Selected roles:', selectedRoles);

        // Update form values (excluding password)
        this.userForm.patchValue({
          username: user.username,
          email: user.email,
          name: user.name,
          phone: user.phone || '',
          is_active: user.is_active,
          roles: selectedRoles, // Use all roles
        });

        // In edit mode, password is initially disabled
        this.userForm.get('password')?.disable();

        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading user: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  enablePasswordField(): void {
    const passwordControl = this.userForm.get('password');

    // Enable the password field
    passwordControl?.enable();

    // Set validators
    passwordControl?.setValidators([
      Validators.required,
      Validators.minLength(6),
    ]);

    // Update validity
    passwordControl?.updateValueAndValidity();

    console.log('Password field enabled, validators applied');
  }

  onSubmit(): void {
    if (this.userForm.invalid) {
      // Mark all fields as touched to trigger validation visuals
      Object.keys(this.userForm.controls).forEach((key) => {
        const control = this.userForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isLoading = true;
    const userData = { ...this.userForm.value };

    // Ensure roles is an array
    if (!userData.roles) {
      userData.roles = [];
    }

    // Password handling logic
    const passwordControl = this.userForm.get('password');

    // If in edit mode and password field is disabled, remove it from the update data
    if (this.isEditMode && passwordControl?.disabled) {
      delete userData.password;
      console.log('Password field is disabled. Not sending password.');
    } else if (
      this.isEditMode &&
      passwordControl?.enabled &&
      !userData.password
    ) {
      // If password field is enabled but empty, remove it to avoid sending empty string
      delete userData.password;
      console.log('Password field is enabled but empty. Not sending password.');
    } else if (userData.password) {
      console.log('Password will be sent for update/create');
    }

    console.log('Submitting user data:', userData);

    if (this.isEditMode && this.userId) {
      // Update existing user
      this.userService.updateUser(this.userId, userData).subscribe({
        next: (response) => {
          console.log('User update response:', response);
          this.isLoading = false;
          this.router.navigate(['/admin/users']);
        },
        error: (error) => {
          console.error('User update error:', error);
          this.errorMessage =
            'Error updating user: ' +
            (error.error?.message || error.message || 'Unknown error');
          this.isLoading = false;
        },
      });
    } else {
      // Create new user
      this.userService.createUser(userData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/users']);
        },
        error: (error) => {
          this.errorMessage =
            'Error creating user: ' +
            (error.error?.message || error.message || 'Unknown error');
          this.isLoading = false;
        },
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/admin/users']);
  }
}
