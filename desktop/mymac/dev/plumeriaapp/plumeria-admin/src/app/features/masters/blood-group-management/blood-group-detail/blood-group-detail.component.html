<div class="blood-group-detail-container">
  <div class="detail-header">
    <h1>Blood Group Details</h1>
    <div class="header-actions">
      <button mat-button color="primary" routerLink="..">
        <mat-icon>arrow_back</mat-icon> Back to Blood Groups
      </button>
    </div>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading blood group details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Blood Group details -->
      <div class="blood-group-info" *ngIf="!isLoading && bloodGroup">
        <div class="blood-group-header">
          <h2>{{ bloodGroup.name }}</h2>
          <div class="status-chip" [ngClass]="bloodGroup.is_active ? 'active' : 'inactive'">
            {{ bloodGroup.is_active ? 'Active' : 'Inactive' }}
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="detail-section">
          <div class="detail-item">
            <div class="label">ID</div>
            <div class="value">{{ bloodGroup.id }}</div>
          </div>

          <div class="detail-item">
            <div class="label">Name</div>
            <div class="value">{{ bloodGroup.name }}</div>
          </div>

          <!-- Code field removed -->

          <div class="detail-item">
            <div class="label">Status</div>
            <div class="value" [ngClass]="bloodGroup.is_active ? 'status-active' : 'status-inactive'">
              {{ bloodGroup.is_active ? 'Active' : 'Inactive' }}
            </div>
          </div>

          <div class="detail-item">
            <div class="label">Created By</div>
            <div class="value">{{ bloodGroup.created_by_username || 'N/A' }}</div>
          </div>

          <div class="detail-item">
            <div class="label">Created At</div>
            <div class="value">{{ formatDate(bloodGroup.created_at) }}</div>
          </div>

          <div class="detail-item" *ngIf="bloodGroup.updated_by">
            <div class="label">Updated By</div>
            <div class="value">{{ bloodGroup.updated_by_username || 'N/A' }}</div>
          </div>

          <div class="detail-item" *ngIf="bloodGroup.updated_at">
            <div class="label">Updated At</div>
            <div class="value">{{ formatDate(bloodGroup.updated_at) }}</div>
          </div>
        </div>

        <div class="detail-actions">
          <button mat-raised-button color="primary" [routerLink]="['../edit', bloodGroup.id]">
            <mat-icon>edit</mat-icon> Edit
          </button>
          <button mat-raised-button color="accent" (click)="toggleStatus()">
            <mat-icon>{{ bloodGroup.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ bloodGroup.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="warn" (click)="deleteBloodGroup()">
            <mat-icon>delete</mat-icon> Delete
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
