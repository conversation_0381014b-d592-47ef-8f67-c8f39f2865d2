<div class="gst-value-detail-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="fas fa-percentage"></i>
        GST Value Details
      </h1>
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
          <li class="breadcrumb-item">
            <a routerLink="/masters/gst-values">GST Values</a>
          </li>
          <li class="breadcrumb-item active">
            Details
          </li>
        </ol>
      </nav>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="loading-container" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <!-- GST Value Details -->
  <div class="detail-container" *ngIf="!loading && gstValue">
    <!-- Main Information Card -->
    <div class="card main-info-card">
      <div class="card-header">
        <div class="header-content">
          <h5 class="card-title mb-0">
            <i class="fas fa-info-circle"></i>
            GST Value Information
          </h5>
          <div class="action-buttons">
            <button
              class="btn btn-outline-primary btn-sm"
              (click)="navigateToEdit()"
              title="Edit GST Value">
              <i class="fas fa-edit"></i>
              Edit
            </button>
            <button
              class="btn btn-sm"
              [class.btn-outline-success]="!gstValue.is_active"
              [class.btn-outline-warning]="gstValue.is_active"
              (click)="toggleStatus()"
              [title]="gstValue.is_active ? 'Deactivate' : 'Activate'">
              <i class="fas" 
                 [class.fa-check]="!gstValue.is_active"
                 [class.fa-ban]="gstValue.is_active"></i>
              {{gstValue.is_active ? 'Deactivate' : 'Activate'}}
            </button>
            <button
              class="btn btn-outline-danger btn-sm"
              (click)="deleteGstValue()"
              title="Delete GST Value">
              <i class="fas fa-trash"></i>
              Delete
            </button>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div class="row">
          <!-- GST Value -->
          <div class="col-md-6">
            <div class="info-item">
              <label class="info-label">
                <i class="fas fa-percentage"></i>
                GST Value
              </label>
              <div class="info-value gst-value-display">
                {{gstValue.value}}%
              </div>
            </div>
          </div>

          <!-- Status -->
          <div class="col-md-6">
            <div class="info-item">
              <label class="info-label">
                <i class="fas fa-toggle-on"></i>
                Status
              </label>
              <div class="info-value">
                <span class="badge" 
                      [class.badge-success]="gstValue.is_active"
                      [class.badge-secondary]="!gstValue.is_active">
                  {{gstValue.is_active ? 'Active' : 'Inactive'}}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Audit Information Card -->
    <div class="card audit-info-card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-history"></i>
          Audit Information
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <!-- Created Information -->
          <div class="col-md-6">
            <div class="audit-section">
              <h6 class="audit-title">
                <i class="fas fa-plus-circle text-success"></i>
                Created
              </h6>
              <div class="audit-details">
                <div class="audit-item">
                  <span class="audit-label">By:</span>
                  <span class="audit-value">{{gstValue.created_by_username || 'N/A'}}</span>
                </div>
                <div class="audit-item">
                  <span class="audit-label">Date:</span>
                  <span class="audit-value">{{gstValue.created_at | date:'medium'}}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Updated Information -->
          <div class="col-md-6">
            <div class="audit-section">
              <h6 class="audit-title">
                <i class="fas fa-edit text-warning"></i>
                Last Updated
              </h6>
              <div class="audit-details">
                <div class="audit-item">
                  <span class="audit-label">By:</span>
                  <span class="audit-value">{{gstValue.updated_by_username || 'N/A'}}</span>
                </div>
                <div class="audit-item">
                  <span class="audit-label">Date:</span>
                  <span class="audit-value">
                    {{gstValue.updated_at ? (gstValue.updated_at | date:'medium') : 'Never'}}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Actions -->
    <div class="navigation-actions">
      <button
        class="btn btn-secondary"
        (click)="navigateToList()">
        <i class="fas fa-arrow-left"></i>
        Back to GST Values
      </button>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="!loading && !gstValue">
    <div class="error-content">
      <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
      <h4>GST Value Not Found</h4>
      <p class="text-muted">The requested GST value could not be found.</p>
      <button
        class="btn btn-primary"
        (click)="navigateToList()">
        <i class="fas fa-arrow-left"></i>
        Back to GST Values
      </button>
    </div>
  </div>
</div>
