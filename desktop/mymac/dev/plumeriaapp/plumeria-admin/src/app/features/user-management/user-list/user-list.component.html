<!-- src/app/features/user-management/user-list/user-list.component.html -->

<div class="user-list-container">
  <div class="user-list-header">
    <h1>User Management</h1>
    <button mat-raised-button color="primary" [routerLink]="['/admin/users/new']">
      <mat-icon>add</mat-icon> Add New User
    </button>
  </div>

  <div class="filter-container">
    <mat-form-field appearance="outline">
      <mat-label>Search Users</mat-label>
      <input matInput [(ngModel)]="searchTerm" (keyup)="applyFilter()" placeholder="Search by name, username, or email">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <div class="mat-elevation-z2 table-container">
    <div *ngIf="isLoading" class="spinner-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading users...</p>
    </div>

    <div *ngIf="!isLoading && errorMessage" class="error-container">
      <p>{{ errorMessage }}</p>
    </div>

    <table mat-table [dataSource]="displayedUsers" *ngIf="!isLoading && !errorMessage" class="user-table" matSort (matSortChange)="onSort($event)">
      <!-- ID Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>ID</th>
        <td mat-cell *matCellDef="let user">{{ user.id }}</td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
        <td mat-cell *matCellDef="let user">{{ user.name || 'N/A' }}</td>
      </ng-container>

      <!-- Username Column -->
      <ng-container matColumnDef="username">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Username</th>
        <td mat-cell *matCellDef="let user">{{ user.username }}</td>
      </ng-container>

      <!-- Email Column -->
      <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
        <td mat-cell *matCellDef="let user">{{ user.email }}</td>
      </ng-container>

      <!-- Roles Column -->
      <ng-container matColumnDef="roles">
        <th mat-header-cell *matHeaderCellDef>Roles</th>
        <td mat-cell *matCellDef="let user">
          <ng-container *ngIf="user.roles && user.roles.length > 0; else noRoles">
            <mat-chip-set>
              <mat-chip *ngFor="let role of user.roles" color="primary" selected>
                {{ role }}
              </mat-chip>
            </mat-chip-set>
          </ng-container>
          <ng-template #noRoles>
            <span class="no-data">No roles assigned</span>
          </ng-template>
        </td>
      </ng-container>

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef>Status</th>
        <td mat-cell *matCellDef="let user">
          <mat-chip [color]="user.is_active ? 'accent' : 'warn'" selected>
            {{ user.is_active ? 'Active' : 'Inactive' }}
          </mat-chip>
        </td>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef>Actions</th>
        <td mat-cell *matCellDef="let user">
          <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="User actions">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #menu="matMenu">
            <a mat-menu-item [routerLink]="['/admin/users', user.id]">
              <mat-icon>visibility</mat-icon>
              <span>View Details</span>
            </a>
            <a mat-menu-item [routerLink]="['/admin/users/edit', user.id]">
              <mat-icon>edit</mat-icon>
              <span>Edit</span>
            </a>
            <button mat-menu-item (click)="toggleUserStatus(user.id, user.is_active || false)">
              <mat-icon>{{ user.is_active ? 'block' : 'check_circle' }}</mat-icon>
              <span>{{ user.is_active ? 'Deactivate' : 'Activate' }}</span>
            </button>
            <button mat-menu-item (click)="deleteUser(user.id)">
              <mat-icon>delete</mat-icon>
              <span>Delete</span>
            </button>
          </mat-menu>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

    <div *ngIf="!isLoading && !errorMessage && filteredUsers.length === 0" class="no-data-container">
      <p>No users found.</p>
    </div>

    <!-- Paginator -->
    <mat-paginator *ngIf="!isLoading && !errorMessage && filteredUsers.length > 0"
                  [length]="totalUsers"
                  [pageSize]="pageSize"
                  [pageSizeOptions]="pageSizeOptions"
                  (page)="onPageChange($event)"
                  aria-label="Select page">
    </mat-paginator>
  </div>
</div>