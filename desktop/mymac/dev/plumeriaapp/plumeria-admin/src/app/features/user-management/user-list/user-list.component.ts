// src/app/features/user-management/user-list/user-list.component.ts

import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule, Sort } from '@angular/material/sort';
import {
  MatPaginatorModule,
  PageEvent,
  MatPaginator,
} from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { User } from '../../../core/models/user';
import { UserService } from '../../../core/services/user.service';

@Component({
  selector: 'app-user-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatMenuModule,
    MatChipsModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './user-list.component.html',
  styleUrl: './user-list.component.scss',
})
export class UserListComponent implements OnInit {
  users: User[] = [];
  filteredUsers: User[] = [];
  displayedUsers: User[] = []; // Users after pagination
  displayedColumns: string[] = [
    'id',
    'name',
    'username',
    'email',
    'roles',
    'status',
    'actions',
  ];
  isLoading = true;
  errorMessage = '';
  searchTerm = '';

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalUsers = 0;

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers(): void {
    this.isLoading = true;
    this.userService.getUsers().subscribe({
      next: (response) => {
        this.users = response.data;
        this.applyFilter(); // This will handle filtering and display the first page
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading users: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    const term = this.searchTerm.toLowerCase();
    this.filteredUsers = this.users.filter(
      (user) =>
        user.name?.toLowerCase().includes(term) ||
        '' ||
        user.username.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term)
    );

    this.totalUsers = this.filteredUsers.length;
    this.updateDisplayedUsers();
  }

  updateDisplayedUsers(): void {
    const startIndex = this.pageIndex * this.pageSize;
    this.displayedUsers = this.filteredUsers.slice(
      startIndex,
      startIndex + this.pageSize
    );
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedUsers();
  }

  onSort(sort: Sort): void {
    const data = [...this.filteredUsers];
    if (!sort.active || sort.direction === '') {
      this.filteredUsers = data;
      this.updateDisplayedUsers();
      return;
    }

    this.filteredUsers = data.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'name':
          return this.compare(a.name || '', b.name || '', isAsc);
        case 'username':
          return this.compare(a.username, b.username, isAsc);
        case 'email':
          return this.compare(a.email, b.email, isAsc);
        case 'id':
          return this.compare(a.id, b.id, isAsc);
        default:
          return 0;
      }
    });

    this.updateDisplayedUsers();
  }

  compare(a: number | string, b: number | string, isAsc: boolean): number {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }

  deleteUser(userId: number): void {
    if (confirm('Are you sure you want to delete this user?')) {
      this.userService.deleteUser(userId).subscribe({
        next: () => {
          this.users = this.users.filter((user) => user.id !== userId);
          this.applyFilter(); // This will update filteredUsers and displayedUsers
        },
        error: (error) => {
          this.errorMessage = 'Error deleting user: ' + error.message;
        },
      });
    }
  }

  toggleUserStatus(userId: number, isActive: boolean): void {
    this.userService.toggleUserStatus(userId, !isActive).subscribe({
      next: (response) => {
        // Update user in both arrays
        this.users = this.users.map((user) =>
          user.id === userId ? { ...user, is_active: !isActive } : user
        );
        this.applyFilter(); // This will update filteredUsers and displayedUsers
      },
      error: (error) => {
        this.errorMessage = 'Error toggling user status: ' + error.message;
      },
    });
  }
}
