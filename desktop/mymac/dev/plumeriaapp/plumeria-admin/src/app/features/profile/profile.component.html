<div class="profile-container">
  <h1 class="page-title">My Profile</h1>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="!isLoading && !errorMessage" class="profile-content">
    <mat-card class="profile-card">
      <mat-card-content>
        <mat-tab-group animationDuration="300ms">
          <!-- Profile Information Tab -->
          <mat-tab label="Profile Information">
            <div class="tab-content">
              <div class="profile-header">
                <div class="profile-avatar">
                  {{ getInitial() }}
                </div>
                <div class="profile-info">
                  <h2>{{ currentUser?.name || 'User' }}</h2>
                  <p class="role-badge" *ngFor="let role of currentUser?.roles">{{ role }}</p>
                </div>
              </div>

              <mat-divider></mat-divider>

              <form [formGroup]="profileForm" (ngSubmit)="updateProfile()" class="profile-form">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Full Name</mat-label>
                    <input matInput formControlName="name" placeholder="Enter your full name">
                    <mat-error *ngIf="profileForm.get('name')?.hasError('required')">
                      Name is required
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Username</mat-label>
                    <input matInput formControlName="username" placeholder="Enter your username">
                    <mat-error *ngIf="profileForm.get('username')?.hasError('required')">
                      Username is required
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Email</mat-label>
                    <input matInput formControlName="email" placeholder="Enter your email" type="email">
                    <mat-error *ngIf="profileForm.get('email')?.hasError('required')">
                      Email is required
                    </mat-error>
                    <mat-error *ngIf="profileForm.get('email')?.hasError('email')">
                      Please enter a valid email address
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Phone</mat-label>
                    <input matInput formControlName="phone" placeholder="Enter your phone number">
                  </mat-form-field>
                </div>

                <div class="form-actions">
                  <button
                    mat-raised-button
                    color="primary"
                    type="submit"
                    [disabled]="profileForm.invalid || isUpdating">
                    <mat-icon>save</mat-icon>
                    Save Changes
                  </button>
                  <mat-spinner *ngIf="isUpdating" diameter="24" class="button-spinner"></mat-spinner>
                </div>
              </form>
            </div>
          </mat-tab>

          <!-- Change Password Tab -->
          <mat-tab label="Change Password">
            <div class="tab-content">
              <form [formGroup]="passwordForm" (ngSubmit)="changePassword()" class="password-form">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Current Password</mat-label>
                    <input
                      matInput
                      [type]="showCurrentPassword ? 'text' : 'password'"
                      formControlName="currentPassword"
                      placeholder="Enter your current password">
                    <button
                      mat-icon-button
                      matSuffix
                      type="button"
                      (click)="togglePasswordVisibility('current')"
                      [attr.aria-label]="'Toggle password visibility'">
                      <mat-icon>{{ showCurrentPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error *ngIf="passwordForm.get('currentPassword')?.hasError('required')">
                      Current password is required
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>New Password</mat-label>
                    <input
                      matInput
                      [type]="showNewPassword ? 'text' : 'password'"
                      formControlName="newPassword"
                      placeholder="Enter your new password">
                    <button
                      mat-icon-button
                      matSuffix
                      type="button"
                      (click)="togglePasswordVisibility('new')"
                      [attr.aria-label]="'Toggle password visibility'">
                      <mat-icon>{{ showNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('required')">
                      New password is required
                    </mat-error>
                    <mat-error *ngIf="passwordForm.get('newPassword')?.hasError('minlength')">
                      Password must be at least 8 characters long
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Confirm Password</mat-label>
                    <input
                      matInput
                      [type]="showConfirmPassword ? 'text' : 'password'"
                      formControlName="confirmPassword"
                      placeholder="Confirm your new password">
                    <button
                      mat-icon-button
                      matSuffix
                      type="button"
                      (click)="togglePasswordVisibility('confirm')"
                      [attr.aria-label]="'Toggle password visibility'">
                      <mat-icon>{{ showConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                    </button>
                    <mat-error *ngIf="passwordForm.get('confirmPassword')?.hasError('required')">
                      Please confirm your password
                    </mat-error>
                    <mat-error *ngIf="passwordForm.hasError('mismatch')">
                      Passwords do not match
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-actions">
                  <button
                    mat-raised-button
                    color="primary"
                    type="submit"
                    [disabled]="passwordForm.invalid || isChangingPassword">
                    <mat-icon>lock</mat-icon>
                    Change Password
                  </button>
                  <mat-spinner *ngIf="isChangingPassword" diameter="24" class="button-spinner"></mat-spinner>
                </div>
              </form>
            </div>
          </mat-tab>
        </mat-tab-group>
      </mat-card-content>
    </mat-card>
  </div>
</div>
