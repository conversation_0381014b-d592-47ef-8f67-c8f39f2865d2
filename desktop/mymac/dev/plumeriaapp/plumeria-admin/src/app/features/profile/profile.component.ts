import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

import { UserService } from '../../core/services/user.service';
import { AuthService } from '../../core/services/auth.service';
import { User } from '../../core/models/user';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatTabsModule,
    MatSlideToggleModule,
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent implements OnInit {
  profileForm!: FormGroup;
  passwordForm!: FormGroup;
  currentUser: User | null = null;
  isLoading = false;
  isUpdating = false;
  isChangingPassword = false;
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForms();
    this.loadUserProfile();
  }

  initForms(): void {
    // Initialize profile form
    this.profileForm = this.fb.group({
      name: ['', Validators.required],
      username: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
    });

    // Initialize password form
    this.passwordForm = this.fb.group(
      {
        currentPassword: ['', Validators.required],
        newPassword: ['', [Validators.required, Validators.minLength(8)]],
        confirmPassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  passwordMatchValidator(form: FormGroup): { mismatch: boolean } | null {
    const newPassword = form.get('newPassword')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;

    return newPassword === confirmPassword ? null : { mismatch: true };
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    const currentUser = this.authService.currentUserValue;
    if (currentUser && currentUser.id) {
      this.userService.getUserById(currentUser.id).subscribe({
        next: (response) => {
          this.currentUser = response.data;
          this.profileForm.patchValue({
            name: this.currentUser.name || '',
            username: this.currentUser.username || '',
            email: this.currentUser.email || '',
            phone: this.currentUser.phone || '',
          });
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage =
            'Failed to load profile: ' + (error.message || 'Unknown error');
          this.isLoading = false;
          this.snackBar.open(this.errorMessage, 'Close', { duration: 5000 });
        },
      });
    } else {
      this.errorMessage = 'User information not available';
      this.isLoading = false;
      this.snackBar.open(this.errorMessage, 'Close', { duration: 5000 });
    }
  }

  updateProfile(): void {
    if (this.profileForm.invalid) {
      return;
    }

    this.isUpdating = true;
    this.errorMessage = '';

    const currentUser = this.authService.currentUserValue;
    if (currentUser && currentUser.id) {
      const updatedUser = {
        ...this.profileForm.value,
      };

      this.userService.updateUser(currentUser.id, updatedUser).subscribe({
        next: (response) => {
          this.isUpdating = false;
          this.snackBar.open('Profile updated successfully', 'Close', {
            duration: 3000,
          });

          // Update the stored user data
          const updatedUserData = {
            ...currentUser,
            ...updatedUser,
          };
          localStorage.setItem('currentUser', JSON.stringify(updatedUserData));
          this.authService.currentUserValue.name = updatedUserData.name;
          this.authService.currentUserValue.email = updatedUserData.email;
          this.authService.currentUserValue.username = updatedUserData.username;
          this.authService.currentUserValue.phone = updatedUserData.phone;
        },
        error: (error) => {
          this.isUpdating = false;
          this.errorMessage =
            'Failed to update profile: ' + (error.message || 'Unknown error');
          this.snackBar.open(this.errorMessage, 'Close', { duration: 5000 });
        },
      });
    }
  }

  changePassword(): void {
    if (this.passwordForm.invalid) {
      return;
    }

    this.isChangingPassword = true;
    this.errorMessage = '';

    const currentUser = this.authService.currentUserValue;
    if (currentUser && currentUser.id) {
      const passwordData = {
        currentPassword: this.passwordForm.value.currentPassword,
        newPassword: this.passwordForm.value.newPassword,
      };

      // Assuming there's an endpoint for changing password
      this.userService.changePassword(currentUser.id, passwordData).subscribe({
        next: () => {
          this.isChangingPassword = false;
          this.passwordForm.reset();
          this.snackBar.open('Password changed successfully', 'Close', {
            duration: 3000,
          });
        },
        error: (error) => {
          this.isChangingPassword = false;
          this.errorMessage =
            'Failed to change password: ' + (error.message || 'Unknown error');
          this.snackBar.open(this.errorMessage, 'Close', { duration: 5000 });
        },
      });
    }
  }

  togglePasswordVisibility(field: string): void {
    if (field === 'current') {
      this.showCurrentPassword = !this.showCurrentPassword;
    } else if (field === 'new') {
      this.showNewPassword = !this.showNewPassword;
    } else if (field === 'confirm') {
      this.showConfirmPassword = !this.showConfirmPassword;
    }
  }

  getInitial(): string {
    if (this.currentUser?.name && this.currentUser.name.length > 0) {
      return this.currentUser.name.charAt(0).toUpperCase();
    } else if (
      this.currentUser?.username &&
      this.currentUser.username.length > 0
    ) {
      return this.currentUser.username.charAt(0).toUpperCase();
    }
    return 'U';
  }
}
