// src/app/core/services/role.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Role } from '../models/user';
import { environment } from '../../../environments/environment';
import { tap, catchError } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class RoleService {
  private apiUrl = `${environment.apiUrl}/roles`;

  constructor(private http: HttpClient) {}

  // Get all roles
  // src/app/core/services/role.service.ts
  getRoles(
    includeInactive = false
  ): Observable<{ success: boolean; data: Role[] }> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    console.log('Calling role API:', url);

    return this.http.get<{ success: boolean; data: Role[] }>(url).pipe(
      tap((response) => console.log('API response:', response)),
      catchError((error) => {
        console.error('API error:', error);
        throw error;
      })
    );
  }

  // Get role by ID
  getRoleById(id: number): Observable<{ success: boolean; data: Role }> {
    return this.http.get<{ success: boolean; data: Role }>(
      `${this.apiUrl}/${id}`
    );
  }

  // Create new role
  createRole(
    role: Partial<Role>
  ): Observable<{ success: boolean; message: string; data: Role }> {
    return this.http.post<{ success: boolean; message: string; data: Role }>(
      this.apiUrl,
      role
    );
  }

  // Update role
  updateRole(
    id: number,
    role: Partial<Role>
  ): Observable<{ success: boolean; message: string; data: Role }> {
    return this.http.put<{ success: boolean; message: string; data: Role }>(
      `${this.apiUrl}/${id}`,
      role
    );
  }

  // Delete role
  deleteRole(id: number): Observable<{ success: boolean; message: string }> {
    return this.http.delete<{ success: boolean; message: string }>(
      `${this.apiUrl}/${id}`
    );
  }

  // Toggle role status
  toggleRoleStatus(
    id: number,
    isActive: boolean
  ): Observable<{ success: boolean; message: string; data: Role }> {
    return this.http.patch<{ success: boolean; message: string; data: Role }>(
      `${this.apiUrl}/${id}/status`,
      { is_active: isActive }
    );
  }

  // Get users with a specific role
  getUsersWithRole(
    roleId: number
  ): Observable<{ success: boolean; data: any[] }> {
    return this.http.get<{ success: boolean; data: any[] }>(
      `${this.apiUrl}/${roleId}/users`
    );
  }

  // Assign role to user
  assignRoleToUser(
    userId: number,
    roleId: number
  ): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(
      `${this.apiUrl}/users/${userId}/${roleId}`,
      {}
    );
  }

  // Remove role from user
  removeRoleFromUser(
    userId: number,
    roleId: number
  ): Observable<{ success: boolean; message: string }> {
    return this.http.delete<{ success: boolean; message: string }>(
      `${this.apiUrl}/users/${userId}/${roleId}`
    );
  }
}
