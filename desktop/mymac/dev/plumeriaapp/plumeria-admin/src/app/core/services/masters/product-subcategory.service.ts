import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, map, tap, throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

// ProductSubcategory interface matching backend model
export interface ProductSubcategory {
  id?: number;
  name: string;
  category_id: number;
  category_name?: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces for API communication
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

@Injectable({
  providedIn: 'root',
})
export class ProductSubcategoryService {
  private apiUrl = `${environment.apiUrl}/masters/product-subcategories`;

  constructor(private http: HttpClient) {}

  /**
   * Get all product subcategories
   */
  getAllProductSubcategories(includeInactive: boolean = false, categoryId?: number): Observable<ProductSubcategory[]> {
    let params = new HttpParams().set('includeInactive', includeInactive.toString());
    
    if (categoryId) {
      params = params.set('categoryId', categoryId.toString());
    }

    return this.http.get<ApiResponse<ProductSubcategory[]>>(this.apiUrl, { params }).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError((error) => {
        console.error('Error fetching product subcategories:', error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Get a product subcategory by ID
   */
  getProductSubcategoryById(id: number): Observable<ProductSubcategory> {
    return this.http.get<ApiResponse<ProductSubcategory>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Product subcategory not found');
      }),
      catchError((error) => {
        console.error(`Error fetching product subcategory with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Create a new product subcategory
   */
  createProductSubcategory(productSubcategory: ProductSubcategory): Observable<ProductSubcategory> {
    return this.http.post<ApiResponse<ProductSubcategory>>(this.apiUrl, productSubcategory).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to create product subcategory');
      }),
      catchError((error) => {
        console.error('Error creating product subcategory:', error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Update an existing product subcategory
   */
  updateProductSubcategory(id: number, productSubcategory: ProductSubcategory): Observable<ProductSubcategory> {
    return this.http.put<ApiResponse<ProductSubcategory>>(`${this.apiUrl}/${id}`, productSubcategory).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to update product subcategory');
      }),
      catchError((error) => {
        console.error(`Error updating product subcategory with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Delete a product subcategory
   */
  deleteProductSubcategory(id: number): Observable<boolean> {
    return this.http.delete<ApiResponse<any>>(`${this.apiUrl}/${id}`).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error(`Error deleting product subcategory with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Toggle product subcategory active status
   */
  toggleProductSubcategoryStatus(id: number, isActive: boolean): Observable<ProductSubcategory> {
    return this.http.patch<ApiResponse<ProductSubcategory>>(`${this.apiUrl}/${id}/status`, { is_active: isActive }).pipe(
      map((response) => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to update product subcategory status');
      }),
      catchError((error) => {
        console.error(`Error toggling product subcategory status with ID ${id}:`, error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Bulk delete product subcategories
   */
  bulkDeleteProductSubcategories(ids: number[]): Observable<boolean> {
    return this.http.post<ApiResponse<any>>(`${this.apiUrl}/bulk-delete`, { ids }).pipe(
      map((response) => {
        return response.success;
      }),
      catchError((error) => {
        console.error('Error bulk deleting product subcategories:', error);
        return throwError(() => new Error(this.getErrorMessage(error)));
      })
    );
  }

  /**
   * Helper method to extract error messages
   */
  private getErrorMessage(error: any): string {
    if (error.error && error.error.message) {
      return error.error.message;
    }
    return error.message || 'An unknown error occurred';
  }
}
