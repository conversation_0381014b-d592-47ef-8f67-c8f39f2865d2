import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { State, StateResponse } from '../../models/masters/state';

@Injectable({
  providedIn: 'root'
})
export class StateService {
  private apiUrl = `${environment.apiUrl}/masters/states`;

  constructor(private http: HttpClient) {}

  // Get all states
  getStates(includeInactive = false, countryId?: number): Observable<StateResponse> {
    let url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    
    if (countryId) {
      url += `&countryId=${countryId}`;
    }
    
    console.log('Fetching states from:', url);
    
    return this.http.get<StateResponse>(url).pipe(
      tap(response => console.log('States API response:', response)),
      catchError(error => {
        console.error('Error fetching states:', error);
        return throwError(() => error);
      })
    );
  }

  // Get state by ID
  getStateById(id: number): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching state by ID from:', url);
    
    return this.http.get<StateResponse>(url).pipe(
      tap(response => console.log('State by ID response:', response)),
      catchError(error => {
        console.error('Error fetching state:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new state
  createState(state: Partial<State>): Observable<StateResponse> {
    console.log('Creating state with data:', state);
    
    return this.http.post<StateResponse>(this.apiUrl, state).pipe(
      tap(response => console.log('Create state response:', response)),
      catchError(error => {
        console.error('Error creating state:', error);
        return throwError(() => error);
      })
    );
  }

  // Update state
  updateState(id: number, state: Partial<State>): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating state at:', url, 'with data:', state);
    
    return this.http.put<StateResponse>(url, state).pipe(
      tap(response => console.log('Update state response:', response)),
      catchError(error => {
        console.error('Error updating state:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete state
  deleteState(id: number): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting state at:', url);
    
    return this.http.delete<StateResponse>(url).pipe(
      tap(response => console.log('Delete state response:', response)),
      catchError(error => {
        console.error('Error deleting state:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle state status
  toggleStateStatus(id: number, isActive: boolean): Observable<StateResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling state status at:', url, 'to:', isActive);
    
    return this.http.patch<StateResponse>(url, { is_active: isActive }).pipe(
      tap(response => console.log('Toggle state status response:', response)),
      catchError(error => {
        console.error('Error toggling state status:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk delete states
  bulkDeleteStates(ids: number[]): Observable<StateResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting states at:', url, 'with IDs:', ids);
    
    return this.http.post<StateResponse>(url, { ids }).pipe(
      tap(response => console.log('Bulk delete states response:', response)),
      catchError(error => {
        console.error('Error bulk deleting states:', error);
        return throwError(() => error);
      })
    );
  }
}
