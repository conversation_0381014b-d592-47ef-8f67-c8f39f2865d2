import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import {
  Staff,
  StaffCreateRequest,
  StaffUpdateRequest,
  StaffListResponse,
  StaffResponse,
  StaffFilters,
} from '../models/staff';

@Injectable({
  providedIn: 'root',
})
export class StaffService {
  private apiUrl = `${environment.apiUrl}/staffs`;

  constructor(private http: HttpClient) {}

  /**
   * Get all staff with optional filtering and pagination
   */
  getStaff(filters: StaffFilters = {}): Observable<StaffListResponse> {
    let params = new HttpParams();

    if (filters.search) {
      params = params.set('search', filters.search);
    }
    if (filters.departmentId) {
      params = params.set('departmentId', filters.departmentId.toString());
    }
    if (filters.designationId) {
      params = params.set('designationId', filters.designationId.toString());
    }
    if (filters.includeInactive !== undefined) {
      params = params.set(
        'includeInactive',
        filters.includeInactive.toString()
      );
    }
    if (filters.page) {
      params = params.set('page', filters.page.toString());
    }
    if (filters.limit) {
      params = params.set('limit', filters.limit.toString());
    }

    return this.http.get<StaffListResponse>(this.apiUrl, { params });
  }

  /**
   * Get staff by ID
   */
  getStaffById(id: number): Observable<StaffResponse> {
    return this.http.get<StaffResponse>(`${this.apiUrl}/${id}`);
  }

  /**
   * Create new staff member
   */
  createStaff(staff: StaffCreateRequest): Observable<StaffResponse> {
    return this.http.post<StaffResponse>(this.apiUrl, staff);
  }

  /**
   * Update staff member
   */
  updateStaff(
    id: number,
    staff: StaffUpdateRequest
  ): Observable<StaffResponse> {
    return this.http.put<StaffResponse>(`${this.apiUrl}/${id}`, staff);
  }

  /**
   * Delete staff member
   */
  deleteStaff(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  /**
   * Toggle staff active status
   */
  toggleStaffStatus(id: number, isActive: boolean): Observable<any> {
    return this.http.patch(`${this.apiUrl}/${id}/status`, {
      is_active: isActive,
    });
  }

  /**
   * Bulk delete staff members
   */
  bulkDeleteStaff(ids: number[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-delete`, { ids });
  }

  /**
   * Get staff by department
   */
  getStaffByDepartment(
    departmentId: number,
    includeInactive = false
  ): Observable<StaffListResponse> {
    let params = new HttpParams();
    if (includeInactive) {
      params = params.set('includeInactive', 'true');
    }

    return this.http.get<StaffListResponse>(
      `${this.apiUrl}/department/${departmentId}`,
      { params }
    );
  }

  /**
   * Upload profile picture
   */
  uploadProfilePicture(id: number, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('profile_picture', file);

    return this.http.post(`${this.apiUrl}/${id}/upload-photo`, formData);
  }

  /**
   * Remove profile picture
   */
  removeProfilePicture(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}/photo`);
  }

  /**
   * Get profile picture URL
   */
  getProfilePictureUrl(profilePicture: string): string {
    if (!profilePicture) {
      return 'https://via.placeholder.com/150x150/e0e0e0/757575?text=No+Photo'; // Default placeholder
    }
    return `${environment.apiUrl.replace('/api', '')}/${profilePicture}`;
  }
}
