import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface GstValue {
  id: number;
  value: number;
  is_active: boolean;
  created_by: number;
  updated_by?: number;
  created_at: string;
  updated_at?: string;
  created_by_username?: string;
  updated_by_username?: string;
}

export interface GstValueResponse {
  success: boolean;
  data: GstValue[];
  message?: string;
}

export interface SingleGstValueResponse {
  success: boolean;
  data: GstValue;
  message?: string;
}

export interface CreateGstValueRequest {
  value: number;
  is_active?: boolean;
}

export interface UpdateGstValueRequest {
  value?: number;
  is_active?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class GstValueService {
  private apiUrl = `${environment.apiUrl}/masters/gst-values`;

  constructor(private http: HttpClient) {}

  // Get all GST values
  getGstValues(includeInactive: boolean = false): Observable<GstValueResponse> {
    let params = new HttpParams();
    if (includeInactive) {
      params = params.set('includeInactive', 'true');
    }
    return this.http.get<GstValueResponse>(this.apiUrl, { params });
  }

  // Get GST value by ID
  getGstValueById(id: number): Observable<SingleGstValueResponse> {
    return this.http.get<SingleGstValueResponse>(`${this.apiUrl}/${id}`);
  }

  // Create new GST value
  createGstValue(gstValue: CreateGstValueRequest): Observable<SingleGstValueResponse> {
    return this.http.post<SingleGstValueResponse>(this.apiUrl, gstValue);
  }

  // Update GST value
  updateGstValue(id: number, gstValue: UpdateGstValueRequest): Observable<SingleGstValueResponse> {
    return this.http.put<SingleGstValueResponse>(`${this.apiUrl}/${id}`, gstValue);
  }

  // Delete GST value
  deleteGstValue(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  // Toggle GST value status
  toggleGstValueStatus(id: number, isActive: boolean): Observable<SingleGstValueResponse> {
    return this.http.patch<SingleGstValueResponse>(`${this.apiUrl}/${id}/status`, { is_active: isActive });
  }

  // Bulk delete GST values
  bulkDeleteGstValues(ids: number[]): Observable<any> {
    return this.http.post(`${this.apiUrl}/bulk-delete`, { ids });
  }
}
