import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { Country, CountryResponse } from '../../models/masters/country';

@Injectable({
  providedIn: 'root'
})
export class CountryService {
  private apiUrl = `${environment.apiUrl}/masters/countries`;

  constructor(private http: HttpClient) {}

  // Get all countries
  getCountries(includeInactive = false): Observable<CountryResponse> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    console.log('Fetching countries from:', url);
    
    return this.http.get<CountryResponse>(url).pipe(
      tap(response => console.log('Countries API response:', response)),
      catchError(error => {
        console.error('Error fetching countries:', error);
        return throwError(() => error);
      })
    );
  }

  // Get country by ID
  getCountryById(id: number): Observable<CountryResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching country by ID from:', url);
    
    return this.http.get<CountryResponse>(url).pipe(
      tap(response => console.log('Country by ID response:', response)),
      catchError(error => {
        console.error('Error fetching country:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new country
  createCountry(country: Partial<Country>): Observable<CountryResponse> {
    console.log('Creating country with data:', country);
    
    return this.http.post<CountryResponse>(this.apiUrl, country).pipe(
      tap(response => console.log('Create country response:', response)),
      catchError(error => {
        console.error('Error creating country:', error);
        return throwError(() => error);
      })
    );
  }

  // Update country
  updateCountry(id: number, country: Partial<Country>): Observable<CountryResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating country at:', url, 'with data:', country);
    
    return this.http.put<CountryResponse>(url, country).pipe(
      tap(response => console.log('Update country response:', response)),
      catchError(error => {
        console.error('Error updating country:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete country
  deleteCountry(id: number): Observable<CountryResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting country at:', url);
    
    return this.http.delete<CountryResponse>(url).pipe(
      tap(response => console.log('Delete country response:', response)),
      catchError(error => {
        console.error('Error deleting country:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle country status
  toggleCountryStatus(id: number, isActive: boolean): Observable<CountryResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling country status at:', url, 'to:', isActive);
    
    return this.http.patch<CountryResponse>(url, { is_active: isActive }).pipe(
      tap(response => console.log('Toggle country status response:', response)),
      catchError(error => {
        console.error('Error toggling country status:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk delete countries
  bulkDeleteCountries(ids: number[]): Observable<CountryResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting countries at:', url, 'with IDs:', ids);
    
    return this.http.post<CountryResponse>(url, { ids }).pipe(
      tap(response => console.log('Bulk delete countries response:', response)),
      catchError(error => {
        console.error('Error bulk deleting countries:', error);
        return throwError(() => error);
      })
    );
  }
}
