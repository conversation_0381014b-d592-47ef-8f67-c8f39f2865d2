import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { Department, DepartmentResponse } from '../../models/masters/department';

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private apiUrl = `${environment.apiUrl}/masters/departments`;

  constructor(private http: HttpClient) {}

  // Get all departments
  getDepartments(includeInactive = false): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    console.log('Fetching departments from:', url);
    
    return this.http.get<DepartmentResponse>(url).pipe(
      tap(response => console.log('Departments API response:', response)),
      catchError(error => {
        console.error('Error fetching departments:', error);
        return throwError(() => error);
      })
    );
  }

  // Get department by ID
  getDepartmentById(id: number): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching department by ID from:', url);
    
    return this.http.get<DepartmentResponse>(url).pipe(
      tap(response => console.log('Department by ID response:', response)),
      catchError(error => {
        console.error('Error fetching department:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new department
  createDepartment(department: Partial<Department>): Observable<DepartmentResponse> {
    console.log('Creating department with data:', department);
    
    return this.http.post<DepartmentResponse>(this.apiUrl, department).pipe(
      tap(response => console.log('Create department response:', response)),
      catchError(error => {
        console.error('Error creating department:', error);
        return throwError(() => error);
      })
    );
  }

  // Update department
  updateDepartment(id: number, department: Partial<Department>): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating department at:', url, 'with data:', department);
    
    return this.http.put<DepartmentResponse>(url, department).pipe(
      tap(response => console.log('Update department response:', response)),
      catchError(error => {
        console.error('Error updating department:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete department
  deleteDepartment(id: number): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting department at:', url);
    
    return this.http.delete<DepartmentResponse>(url).pipe(
      tap(response => console.log('Delete department response:', response)),
      catchError(error => {
        console.error('Error deleting department:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle department status
  toggleDepartmentStatus(id: number, isActive: boolean): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling department status at:', url, 'to:', isActive);
    
    return this.http.patch<DepartmentResponse>(url, { is_active: isActive }).pipe(
      tap(response => console.log('Toggle department status response:', response)),
      catchError(error => {
        console.error('Error toggling department status:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk delete departments
  bulkDeleteDepartments(ids: number[]): Observable<DepartmentResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting departments at:', url, 'with IDs:', ids);
    
    return this.http.post<DepartmentResponse>(url, { ids }).pipe(
      tap(response => console.log('Bulk delete departments response:', response)),
      catchError(error => {
        console.error('Error bulk deleting departments:', error);
        return throwError(() => error);
      })
    );
  }
}
