import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { City, CityResponse } from '../../models/masters/city';

@Injectable({
  providedIn: 'root'
})
export class CityService {
  private apiUrl = `${environment.apiUrl}/masters/cities`;

  constructor(private http: HttpClient) {}

  // Get all cities
  getCities(includeInactive = false, stateId?: number, countryId?: number): Observable<CityResponse> {
    let url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    
    if (stateId) {
      url += `&stateId=${stateId}`;
    }
    
    if (countryId) {
      url += `&countryId=${countryId}`;
    }
    
    console.log('Fetching cities from:', url);
    
    return this.http.get<CityResponse>(url).pipe(
      tap(response => console.log('Cities API response:', response)),
      catchError(error => {
        console.error('Error fetching cities:', error);
        return throwError(() => error);
      })
    );
  }

  // Get city by ID
  getCityById(id: number): Observable<CityResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching city by ID from:', url);
    
    return this.http.get<CityResponse>(url).pipe(
      tap(response => console.log('City by ID response:', response)),
      catchError(error => {
        console.error('Error fetching city:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new city
  createCity(city: Partial<City>): Observable<CityResponse> {
    console.log('Creating city with data:', city);
    
    return this.http.post<CityResponse>(this.apiUrl, city).pipe(
      tap(response => console.log('Create city response:', response)),
      catchError(error => {
        console.error('Error creating city:', error);
        return throwError(() => error);
      })
    );
  }

  // Update city
  updateCity(id: number, city: Partial<City>): Observable<CityResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating city at:', url, 'with data:', city);
    
    return this.http.put<CityResponse>(url, city).pipe(
      tap(response => console.log('Update city response:', response)),
      catchError(error => {
        console.error('Error updating city:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete city
  deleteCity(id: number): Observable<CityResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting city at:', url);
    
    return this.http.delete<CityResponse>(url).pipe(
      tap(response => console.log('Delete city response:', response)),
      catchError(error => {
        console.error('Error deleting city:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle city status
  toggleCityStatus(id: number, isActive: boolean): Observable<CityResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling city status at:', url, 'to:', isActive);
    
    return this.http.patch<CityResponse>(url, { is_active: isActive }).pipe(
      tap(response => console.log('Toggle city status response:', response)),
      catchError(error => {
        console.error('Error toggling city status:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk delete cities
  bulkDeleteCities(ids: number[]): Observable<CityResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting cities at:', url, 'with IDs:', ids);
    
    return this.http.post<CityResponse>(url, { ids }).pipe(
      tap(response => console.log('Bulk delete cities response:', response)),
      catchError(error => {
        console.error('Error bulk deleting cities:', error);
        return throwError(() => error);
      })
    );
  }
}
