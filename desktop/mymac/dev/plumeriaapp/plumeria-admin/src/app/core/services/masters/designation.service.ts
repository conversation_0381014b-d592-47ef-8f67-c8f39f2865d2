import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { Designation, DesignationResponse } from '../../models/masters/designation';

@Injectable({
  providedIn: 'root'
})
export class DesignationService {
  private apiUrl = `${environment.apiUrl}/masters/designations`;

  constructor(private http: HttpClient) {}

  // Get all designations
  getDesignations(includeInactive = false): Observable<DesignationResponse> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;
    console.log('Fetching designations from:', url);
    
    return this.http.get<DesignationResponse>(url).pipe(
      tap(response => console.log('Designations API response:', response)),
      catchError(error => {
        console.error('Error fetching designations:', error);
        return throwError(() => error);
      })
    );
  }

  // Get designation by ID
  getDesignationById(id: number): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching designation by ID from:', url);
    
    return this.http.get<DesignationResponse>(url).pipe(
      tap(response => console.log('Designation by ID response:', response)),
      catchError(error => {
        console.error('Error fetching designation:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new designation
  createDesignation(designation: Partial<Designation>): Observable<DesignationResponse> {
    console.log('Creating designation with data:', designation);
    
    return this.http.post<DesignationResponse>(this.apiUrl, designation).pipe(
      tap(response => console.log('Create designation response:', response)),
      catchError(error => {
        console.error('Error creating designation:', error);
        return throwError(() => error);
      })
    );
  }

  // Update designation
  updateDesignation(id: number, designation: Partial<Designation>): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating designation at:', url, 'with data:', designation);
    
    return this.http.put<DesignationResponse>(url, designation).pipe(
      tap(response => console.log('Update designation response:', response)),
      catchError(error => {
        console.error('Error updating designation:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete designation
  deleteDesignation(id: number): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting designation at:', url);
    
    return this.http.delete<DesignationResponse>(url).pipe(
      tap(response => console.log('Delete designation response:', response)),
      catchError(error => {
        console.error('Error deleting designation:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle designation status
  toggleDesignationStatus(id: number, isActive: boolean): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling designation status at:', url, 'to:', isActive);
    
    return this.http.patch<DesignationResponse>(url, { is_active: isActive }).pipe(
      tap(response => console.log('Toggle designation status response:', response)),
      catchError(error => {
        console.error('Error toggling designation status:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk delete designations
  bulkDeleteDesignations(ids: number[]): Observable<DesignationResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting designations at:', url, 'with IDs:', ids);
    
    return this.http.post<DesignationResponse>(url, { ids }).pipe(
      tap(response => console.log('Bulk delete designations response:', response)),
      catchError(error => {
        console.error('Error bulk deleting designations:', error);
        return throwError(() => error);
      })
    );
  }
}
