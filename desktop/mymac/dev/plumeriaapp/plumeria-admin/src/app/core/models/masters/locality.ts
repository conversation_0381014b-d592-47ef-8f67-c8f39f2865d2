export interface Locality {
  id: number;
  name: string;
  city_id: number;
  city_name?: string;
  state_name?: string;
  country_name?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: string;
  updated_at?: string;
  created_by_username?: string;
  updated_by_username?: string;
}

export interface LocalityResponse {
  success: boolean;
  data: Locality | Locality[];
  message?: string;
}
