import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

export interface BloodGroup {
  id: number;
  name: string;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  updated_by?: number;
  created_by_username?: string;
  updated_by_username?: string;
}

export interface BloodGroupResponse {
  success: boolean;
  message?: string;
  data: BloodGroup[] | BloodGroup;
}

@Injectable({
  providedIn: 'root',
})
export class BloodGroupService {
  private apiUrl = `${environment.apiUrl}/masters/blood-groups`;

  constructor(private http: HttpClient) {}

  // Get all blood groups
  getBloodGroups(includeInactive = false): Observable<BloodGroupResponse> {
    const url = `${this.apiUrl}?includeInactive=${includeInactive}`;

    return this.http.get<BloodGroupResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Get blood group by ID
  getBloodGroupById(id: number): Observable<BloodGroupResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching blood group details from:', url);

    return this.http.get<BloodGroupResponse>(url).pipe(
      tap((response) =>
        console.log('Blood Group details API response:', response)
      ),
      catchError((error) => {
        console.error('Error fetching blood group details:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new blood group
  createBloodGroup(
    bloodGroup: Partial<BloodGroup>
  ): Observable<BloodGroupResponse> {
    console.log('Creating blood group:', bloodGroup);

    return this.http.post<BloodGroupResponse>(this.apiUrl, bloodGroup).pipe(
      tap((response) =>
        console.log('Create blood group API response:', response)
      ),
      catchError((error) => {
        console.error('Error creating blood group:', error);
        return throwError(() => error);
      })
    );
  }

  // Update blood group
  updateBloodGroup(
    id: number,
    bloodGroup: Partial<BloodGroup>
  ): Observable<BloodGroupResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating blood group:', id, bloodGroup);

    return this.http.put<BloodGroupResponse>(url, bloodGroup).pipe(
      tap((response) =>
        console.log('Update blood group API response:', response)
      ),
      catchError((error) => {
        console.error('Error updating blood group:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete blood group
  deleteBloodGroup(id: number): Observable<BloodGroupResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting blood group:', id);

    return this.http.delete<BloodGroupResponse>(url).pipe(
      tap((response) =>
        console.log('Delete blood group API response:', response)
      ),
      catchError((error) => {
        console.error('Error deleting blood group:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle blood group status
  toggleBloodGroupStatus(
    id: number,
    isActive: boolean
  ): Observable<BloodGroupResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling blood group status:', id, isActive);

    return this.http
      .patch<BloodGroupResponse>(url, { is_active: isActive })
      .pipe(
        tap((response) =>
          console.log('Toggle blood group status API response:', response)
        ),
        catchError((error) => {
          console.error('Error toggling blood group status:', error);
          return throwError(() => error);
        })
      );
  }

  // Bulk delete blood groups
  bulkDeleteBloodGroups(ids: number[]): Observable<BloodGroupResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting blood groups:', ids);

    return this.http.post<BloodGroupResponse>(url, { ids }).pipe(
      tap((response) =>
        console.log('Bulk delete blood groups API response:', response)
      ),
      catchError((error) => {
        console.error('Error bulk deleting blood groups:', error);
        return throwError(() => error);
      })
    );
  }
}
