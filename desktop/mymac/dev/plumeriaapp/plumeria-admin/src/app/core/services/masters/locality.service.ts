import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { Locality, LocalityResponse } from '../../models/masters/locality';

@Injectable({
  providedIn: 'root',
})
export class LocalityService {
  private apiUrl = `${environment.apiUrl}/masters/localities`;

  constructor(private http: HttpClient) {}

  // Get all localities
  getLocalities(
    includeInactive = false,
    cityId?: number,
    stateId?: number
  ): Observable<LocalityResponse> {
    let url = `${this.apiUrl}?includeInactive=${includeInactive}`;

    if (cityId) {
      url += `&cityId=${cityId}`;
    }

    if (stateId) {
      url += `&stateId=${stateId}`;
    }

    return this.http.get<LocalityResponse>(url).pipe(
      catchError((error) => {
        return throwError(() => error);
      })
    );
  }

  // Get locality by ID
  getLocalityById(id: number): Observable<LocalityResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Fetching locality by ID from:', url);

    return this.http.get<LocalityResponse>(url).pipe(
      tap((response) => console.log('Locality by ID response:', response)),
      catchError((error) => {
        console.error('Error fetching locality:', error);
        return throwError(() => error);
      })
    );
  }

  // Create new locality
  createLocality(locality: Partial<Locality>): Observable<LocalityResponse> {
    console.log('Creating locality with data:', locality);
    console.log('API URL:', this.apiUrl);

    // Add more detailed logging
    return this.http.post<LocalityResponse>(this.apiUrl, locality).pipe(
      tap((response) => {
        console.log('Create locality response:', response);
        console.log('Response type:', typeof response);
        console.log('Response structure:', JSON.stringify(response));
      }),
      catchError((error) => {
        console.error('Error creating locality:', error);
        console.error('Error status:', error.status);
        console.error('Error message:', error.message);
        console.error('Error details:', error.error);
        return throwError(() => error);
      })
    );
  }

  // Update locality
  updateLocality(
    id: number,
    locality: Partial<Locality>
  ): Observable<LocalityResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Updating locality at:', url, 'with data:', locality);

    return this.http.put<LocalityResponse>(url, locality).pipe(
      tap((response) => console.log('Update locality response:', response)),
      catchError((error) => {
        console.error('Error updating locality:', error);
        return throwError(() => error);
      })
    );
  }

  // Delete locality
  deleteLocality(id: number): Observable<LocalityResponse> {
    const url = `${this.apiUrl}/${id}`;
    console.log('Deleting locality at:', url);

    return this.http.delete<LocalityResponse>(url).pipe(
      tap((response) => console.log('Delete locality response:', response)),
      catchError((error) => {
        console.error('Error deleting locality:', error);
        return throwError(() => error);
      })
    );
  }

  // Toggle locality status
  toggleLocalityStatus(
    id: number,
    isActive: boolean
  ): Observable<LocalityResponse> {
    const url = `${this.apiUrl}/${id}/status`;
    console.log('Toggling locality status at:', url, 'to:', isActive);

    return this.http.patch<LocalityResponse>(url, { is_active: isActive }).pipe(
      tap((response) =>
        console.log('Toggle locality status response:', response)
      ),
      catchError((error) => {
        console.error('Error toggling locality status:', error);
        return throwError(() => error);
      })
    );
  }

  // Bulk delete localities
  bulkDeleteLocalities(ids: number[]): Observable<LocalityResponse> {
    const url = `${this.apiUrl}/bulk-delete`;
    console.log('Bulk deleting localities at:', url, 'with IDs:', ids);

    return this.http.post<LocalityResponse>(url, { ids }).pipe(
      tap((response) =>
        console.log('Bulk delete localities response:', response)
      ),
      catchError((error) => {
        console.error('Error bulk deleting localities:', error);
        return throwError(() => error);
      })
    );
  }
}
