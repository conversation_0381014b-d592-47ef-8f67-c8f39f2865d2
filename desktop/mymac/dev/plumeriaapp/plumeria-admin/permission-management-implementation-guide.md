# Permission Management Implementation Guide

## Overview

This document outlines the implementation requirements for the Permission Management system in the Plumeria Admin application. The system follows a Role-Based Access Control (RBAC) model where permissions are assigned to roles for specific modules.

## Current Database Structure

The database already has the following structure:

### Permissions Table
```sql
+----+----------+---------------------------+---------------------+
| id | name     | description               | created_at          |
+----+----------+---------------------------+---------------------+
|  1 | create   | Create new resources      | 2025-04-27 17:13:57 |
|  2 | read     | View existing resources   | 2025-04-27 17:13:57 |
|  3 | update   | Modify existing resources | 2025-04-27 17:13:57 |
|  4 | delete   | Remove existing resources | 2025-04-27 17:13:57 |
+----+----------+---------------------------+---------------------+
```

### Role_Module_Permissions Table
```sql
+---------+-----------+---------------+
| role_id | module_id | permission_id |
+---------+-----------+---------------+
|       1 |         1 |             1 |
|       1 |         1 |             2 |
|       1 |         1 |             3 |
|       1 |         1 |             4 |
|       2 |         1 |             2 |
|       1 |         2 |             1 |
|       1 |         2 |             2 |
|       1 |         2 |             3 |
|       1 |         2 |             4 |
|       2 |         2 |             1 |
|       2 |         2 |             2 |
|       1 |         3 |             1 |
|       1 |         3 |             2 |
|       1 |         3 |             3 |
|       1 |         3 |             4 |
|       1 |         4 |             1 |
|       1 |         4 |             2 |
|       1 |         4 |             3 |
|       1 |         4 |             4 |
|       1 |         5 |             1 |
|       1 |         5 |             2 |
|       1 |         5 |             3 |
|       1 |         5 |             4 |
+---------+-----------+---------------+
```

## Implementation Requirements

### 1. Backend API Endpoints

#### 1.1 Permission Endpoints

- **Get All Permissions**
  - Endpoint: `GET /api/permissions`
  - Description: Retrieves all available permissions
  - Response: List of permission objects with id, name, and description

- **Get Permissions for Role**
  - Endpoint: `GET /api/role-module-permissions/roles/:roleId`
  - Description: Retrieves all permissions assigned to a specific role across all modules
  - Response: List of role-module-permission objects

- **Get Permissions for Role-Module**
  - Endpoint: `GET /api/role-module-permissions/roles/:roleId/modules/:moduleId`
  - Description: Retrieves permissions assigned to a specific role for a specific module
  - Response: List of role-module-permission objects

- **Assign Permission**
  - Endpoint: `POST /api/role-module-permissions/roles/:roleId/modules/:moduleId/permissions/:permissionId`
  - Description: Assigns a specific permission to a role for a module
  - Response: Success message

- **Remove Permission**
  - Endpoint: `DELETE /api/role-module-permissions/roles/:roleId/modules/:moduleId/permissions/:permissionId`
  - Description: Removes a specific permission from a role for a module
  - Response: Success message

- **Assign All Permissions**
  - Endpoint: `POST /api/role-module-permissions/roles/:roleId/modules/:moduleId/permissions`
  - Description: Assigns all permissions to a role for a module
  - Response: Success message

- **Remove All Permissions**
  - Endpoint: `DELETE /api/role-module-permissions/roles/:roleId/modules/:moduleId/permissions`
  - Description: Removes all permissions from a role for a module
  - Response: Success message

#### 1.2 Permission Checking Endpoint

- **Check Permission**
  - Endpoint: `POST /api/auth/check-permission`
  - Description: Checks if the current user has a specific permission for a module
  - Request Body: `{ module: string, permission: string }`
  - Response: `{ hasPermission: boolean }`

### 2. Backend Implementation

#### 2.1 Permission Service

Create a service that handles:

- Retrieving permissions from the database
- Assigning/removing permissions for role-module combinations
- Checking if a user has a specific permission

#### 2.2 Permission Controller

Create a controller that:

- Handles API requests for permission management
- Validates input data
- Calls appropriate service methods
- Returns formatted responses

#### 2.3 Permission Middleware

Create middleware that:

- Extracts user information from JWT token
- Checks if the user has the required permission for protected routes
- Redirects to forbidden page if permission is denied

### 3. Frontend Implementation

#### 3.1 Permission Management Component

The Permission Management component should:

- Allow selecting a role from a dropdown
- Allow selecting a module from a dropdown
- Display checkboxes for each permission (create, read, update, delete)
- Show the current permission state (checked/unchecked)
- Allow toggling individual permissions
- Provide buttons for "Assign All" and "Remove All" operations

#### 3.2 Permission Service

Create a service that:

- Makes API calls to the backend permission endpoints
- Caches permission results for better performance
- Provides methods for checking permissions in the UI

#### 3.3 Permission Guard

Create a route guard that:

- Checks if the user has the required permission before accessing a route
- Redirects to the forbidden page if permission is denied

### 4. Integration with Existing Components

#### 4.1 Navigation

- Update the sidebar to show/hide menu items based on user permissions
- Use the permission service to check if the user has access to each route

#### 4.2 UI Components

- Disable or hide UI elements (buttons, forms, etc.) based on user permissions
- Add permission checks to sensitive operations

## Implementation Steps

### Step 1: Backend API Development

1. Create database models for permissions and role-module-permissions
2. Implement the permission service with methods for all required operations
3. Create API endpoints for permission management
4. Implement permission checking logic
5. Add middleware for protecting routes based on permissions

### Step 2: Frontend Development

1. Create the permission management component
2. Implement the permission service for API communication
3. Create the permission guard for route protection
4. Update the navigation to respect permissions
5. Add permission checks to UI components

### Step 3: Testing

1. Test permission assignment and removal
2. Test permission checking for different user roles
3. Test route protection with the permission guard
4. Test UI behavior based on permissions

### Step 4: Documentation

1. Document the permission API endpoints
2. Create user documentation for the permission management UI
3. Document the permission checking process for developers

## Best Practices

1. **Caching**: Cache permission results to improve performance
2. **Error Handling**: Provide clear error messages for permission operations
3. **Validation**: Validate all inputs to prevent security issues
4. **Logging**: Log permission changes for audit purposes
5. **Default Permissions**: Set up sensible default permissions for common roles

## Security Considerations

1. **Principle of Least Privilege**: Assign only the permissions necessary for each role
2. **Admin Protection**: Implement special checks for admin-level operations
3. **Permission Verification**: Always verify permissions on the backend, never trust client-side checks
4. **Audit Trail**: Keep a record of permission changes
5. **Session Validation**: Ensure the user session is valid before checking permissions

## Conclusion

Implementing this permission management system will provide a flexible and secure way to control access to different parts of the application. By following the RBAC model with module-specific permissions, the system can scale to accommodate complex access control requirements while remaining maintainable.
