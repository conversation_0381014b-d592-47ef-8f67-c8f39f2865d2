const db = require('../config/database');

/**
 * Staff Model - Following the exact pattern of your working BloodGroup model
 */
const Staff = {
  /**
   * Get all staff - Following BloodGroup.findAll pattern
   */
  findAll: async (includeInactive = false) => {
    console.log('=== Staff.findAll - Simple Pattern ===');
    console.log('includeInactive:', includeInactive);
    
    let query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `;

    if (!includeInactive) {
      query += ' WHERE s.is_active = 1';
    }

    query += ' ORDER BY s.staff_name ASC';

    console.log('Query:', query);
    console.log('No parameters - following BloodGroup pattern');

    return await db.query(query);
  },

  /**
   * Get staff with pagination and filters - NEW METHOD
   */
  findAllWithFilters: async (options = {}) => {
    console.log('=== Staff.findAllWithFilters ===');
    console.log('Options:', JSON.stringify(options, null, 2));
    
    const {
      includeInactive = false,
      departmentId,
      designationId,
      search,
      limit,
      offset
    } = options;

    let query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `;

    const conditions = [];
    const params = [];

    // Active status filter
    if (!includeInactive) {
      conditions.push('s.is_active = ?');
      params.push(1);
    }

    // Department filter
    if (departmentId && !isNaN(parseInt(departmentId))) {
      conditions.push('s.department_id = ?');
      params.push(parseInt(departmentId));
    }

    // Designation filter
    if (designationId && !isNaN(parseInt(designationId))) {
      conditions.push('s.designation_id = ?');
      params.push(parseInt(designationId));
    }

    // Search filter
    if (search && typeof search === 'string' && search.trim().length > 0) {
      conditions.push('(s.staff_name LIKE ? OR s.staff_email LIKE ? OR s.staff_mobile LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Add WHERE clause
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Add ordering
    query += ' ORDER BY s.staff_name ASC';

    // Add pagination if provided
    if (limit && !isNaN(parseInt(limit)) && parseInt(limit) > 0) {
      query += ' LIMIT ?';
      params.push(parseInt(limit));

      if (offset && !isNaN(parseInt(offset)) && parseInt(offset) >= 0) {
        query += ' OFFSET ?';
        params.push(parseInt(offset));
      }
    }

    console.log('Final Query:', query);
    console.log('Final Params:', params);
    console.log('Param count:', params.length);
    console.log('Placeholder count:', (query.match(/\?/g) || []).length);

    return await db.query(query, params);
  },

  /**
   * Get total count
   */
  getCount: async (options = {}) => {
    console.log('=== Staff.getCount ===');
    
    const {
      includeInactive = false,
      departmentId,
      designationId,
      search
    } = options;

    let query = 'SELECT COUNT(*) as total FROM staffs s';
    const conditions = [];
    const params = [];

    // Active status filter
    if (!includeInactive) {
      conditions.push('s.is_active = ?');
      params.push(1);
    }

    // Department filter
    if (departmentId && !isNaN(parseInt(departmentId))) {
      conditions.push('s.department_id = ?');
      params.push(parseInt(departmentId));
    }

    // Designation filter
    if (designationId && !isNaN(parseInt(designationId))) {
      conditions.push('s.designation_id = ?');
      params.push(parseInt(designationId));
    }

    // Search filter
    if (search && typeof search === 'string' && search.trim().length > 0) {
      conditions.push('(s.staff_name LIKE ? OR s.staff_email LIKE ? OR s.staff_mobile LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Add WHERE clause
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    console.log('Count Query:', query);
    console.log('Count Params:', params);

    const result = await db.query(query, params);
    return result[0].total;
  },

  /**
   * Get staff by ID - Following BloodGroup pattern
   */
  findById: async (id) => {
    const query = `
      SELECT s.*,
             d.name as designation_name,
             dept.name as department_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
      WHERE s.id = ?
    `;

    const staff = await db.query(query, [id]);
    return staff[0];
  },

  /**
   * Get staff by name - Following BloodGroup pattern
   */
  findByName: async (name) => {
    const query = 'SELECT * FROM staffs WHERE staff_name = ?';
    const staff = await db.query(query, [name]);
    return staff[0];
  },

  /**
   * Get staff by email - Following BloodGroup pattern
   */
  findByEmail: async (email) => {
    const query = 'SELECT * FROM staffs WHERE staff_email = ?';
    const staff = await db.query(query, [email]);
    return staff[0];
  },

  /**
   * Get staff by mobile - Following BloodGroup pattern
   */
  findByMobile: async (mobile) => {
    const query = 'SELECT * FROM staffs WHERE staff_mobile = ?';
    const staff = await db.query(query, [mobile]);
    return staff[0];
  },

  /**
   * Get staff by Aadhaar - Following BloodGroup pattern
   */
  findByAadhaar: async (aadhaar) => {
    const query = 'SELECT * FROM staffs WHERE aadhaar_number = ?';
    const staff = await db.query(query, [aadhaar]);
    return staff[0];
  },

  /**
   * Get staff by PAN - Following BloodGroup pattern
   */
  findByPan: async (pan) => {
    const query = 'SELECT * FROM staffs WHERE pan_number = ?';
    const staff = await db.query(query, [pan]);
    return staff[0];
  },

  /**
   * Get staff by department
   */
  findByDepartment: async (departmentId, includeInactive = false) => {
    let query = `
      SELECT s.*, d.name as designation_name, dept.name as department_name
      FROM staffs s
      LEFT JOIN designations d ON s.designation_id = d.id
      LEFT JOIN departments dept ON s.department_id = dept.id
      WHERE s.department_id = ?
    `;

    const params = [departmentId];

    if (!includeInactive) {
      query += ' AND s.is_active = ?';
      params.push(1);
    }

    query += ' ORDER BY s.staff_name ASC';

    return await db.query(query, params);
  },

  /**
   * Create new staff - Following BloodGroup pattern
   */
  create: async (staffData) => {
    const {
      staff_name,
      staff_mobile,
      staff_email,
      gender,
      user_role_id,
      date_of_birth,
      marital_status,
      blood_group_id,
      joining_date,
      emergency_contact_name,
      emergency_contact_phone,
      emergency_contact_relation,
      designation_id,
      employment_type_id,
      health_insurance_provider,
      health_insurance_number,
      department_id,
      salary_amount,
      salary_last_hiked_date,
      staff_address,
      locality_id,
      city_id,
      pincode,
      state_id,
      aadhaar_number,
      pan_number,
      qualification_id,
      bank_name,
      account_number,
      ifsc_code,
      profile_picture,
      is_active = 1,
      created_by
    } = staffData;

    const query = `
      INSERT INTO staffs (
        staff_name, staff_mobile, staff_email, gender, user_role_id,
        date_of_birth, marital_status, blood_group_id, joining_date,
        emergency_contact_name, emergency_contact_phone, emergency_contact_relation,
        designation_id, employment_type_id, health_insurance_provider,
        health_insurance_number, department_id, salary_amount, salary_last_hiked_date,
        staff_address, locality_id, city_id, pincode, state_id,
        aadhaar_number, pan_number, qualification_id, bank_name,
        account_number, ifsc_code, profile_picture, is_active, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(query, [
      staff_name, staff_mobile, staff_email, gender, user_role_id,
      date_of_birth, marital_status, blood_group_id, joining_date,
      emergency_contact_name, emergency_contact_phone, emergency_contact_relation,
      designation_id, employment_type_id, health_insurance_provider,
      health_insurance_number, department_id, salary_amount, salary_last_hiked_date,
      staff_address, locality_id, city_id, pincode, state_id,
      aadhaar_number, pan_number, qualification_id, bank_name,
      account_number, ifsc_code, profile_picture, is_active, created_by
    ]);

    return { id: result.insertId, staff_name, staff_email, staff_mobile, is_active, created_by };
  },

  /**
   * Update staff - Following BloodGroup pattern
   */
  update: async (id, staffData) => {
    let query = 'UPDATE staffs SET ';
    const params = [];
    const updates = [];

    // Build dynamic update query like BloodGroup
    Object.keys(staffData).forEach(key => {
      if (staffData[key] !== undefined && key !== 'id') {
        updates.push(`${key} = ?`);
        params.push(staffData[key]);
      }
    });

    if (updates.length === 0) {
      throw new Error('No fields to update');
    }

    query += updates.join(', ') + ' WHERE id = ?';
    params.push(id);

    await db.query(query, params);
    return { id, ...staffData };
  },

  /**
   * Delete staff - Following BloodGroup pattern
   */
  delete: async (id) => {
    const query = 'DELETE FROM staffs WHERE id = ?';
    return await db.query(query, [id]);
  },

  /**
   * Bulk delete - Following BloodGroup pattern
   */
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid staff IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM staffs WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  /**
   * Toggle active status - Following BloodGroup pattern
   */
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE staffs SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  },

  /**
   * Update profile picture
   */
  updateProfilePicture: async (id, profilePicture, updatedBy) => {
    const query = 'UPDATE staffs SET profile_picture = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [profilePicture, updatedBy, id]);
    return { id, profile_picture: profilePicture };
  },

  /**
   * Remove profile picture
   */
  removeProfilePicture: async (id, updatedBy) => {
    const query = 'UPDATE staffs SET profile_picture = NULL, updated_by = ? WHERE id = ?';
    await db.query(query, [updatedBy, id]);
    return { id, profile_picture: null };
  }
};

module.exports = Staff;