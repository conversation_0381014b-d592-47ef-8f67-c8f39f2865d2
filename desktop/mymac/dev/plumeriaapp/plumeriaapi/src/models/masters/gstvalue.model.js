// src/models/masters/gstvalue.model.js
const db = require('../../config/database');

const GstValue = {
  // Get all GST values
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT gv.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM gst_values gv
      LEFT JOIN users u1 ON gv.created_by = u1.id
      LEFT JOIN users u2 ON gv.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE gv.is_active = 1';
    }
    
    query += ' ORDER BY gv.value ASC';
    
    return await db.query(query);
  },

  // Get GST value by ID
  findById: async (id) => {
    const query = `
      SELECT gv.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM gst_values gv
      LEFT JOIN users u1 ON gv.created_by = u1.id
      LEFT JOIN users u2 ON gv.updated_by = u2.id
      WHERE gv.id = ?
    `;
    
    const gstValues = await db.query(query, [id]);
    return gstValues[0];
  },

  // Get GST value by value
  findByValue: async (value) => {
    const query = 'SELECT * FROM gst_values WHERE value = ?';
    const gstValues = await db.query(query, [value]);
    return gstValues[0];
  },

  // Create new GST value
  create: async (gstValueData) => {
    const { value, is_active = 1, created_by } = gstValueData;
    
    const query = `
      INSERT INTO gst_values (value, is_active, created_by) 
      VALUES (?, ?, ?)
    `;
    
    const result = await db.query(query, [value, is_active, created_by]);
    return { id: result.insertId, value, is_active, created_by };
  },

  // Update GST value
  update: async (id, gstValueData) => {
    const { value, is_active, updated_by } = gstValueData;
    
    const updates = [];
    const params = [];
    
    if (value !== undefined) {
      updates.push('value = ?');
      params.push(value);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE gst_values SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...gstValueData };
  },

  // Delete GST value
  delete: async (id) => {
    const query = 'DELETE FROM gst_values WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete GST values
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid GST value IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM gst_values WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle GST value active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE gst_values SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = GstValue;
