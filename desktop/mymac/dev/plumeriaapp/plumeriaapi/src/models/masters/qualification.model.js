// src/models/masters/qualification.model.js
const db = require('../../config/database');

const Qualification = {
  // Get all qualifications
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT q.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM qualifications q
      LEFT JOIN users u1 ON q.created_by = u1.id
      LEFT JOIN users u2 ON q.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE q.is_active = 1';
    }
    
    query += ' ORDER BY q.id DESC';
    
    return await db.query(query);
  },

  // Get qualification by ID
  findById: async (id) => {
    const query = `
      SELECT q.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM qualifications q
      LEFT JOIN users u1 ON q.created_by = u1.id
      LEFT JOIN users u2 ON q.updated_by = u2.id
      WHERE q.id = ?
    `;
    
    const qualifications = await db.query(query, [id]);
    return qualifications[0];
  },

  // Get qualification by name
  findByName: async (name) => {
    const query = 'SELECT * FROM qualifications WHERE name = ?';
    const qualifications = await db.query(query, [name]);
    return qualifications[0];
  },

  // Create new qualification
  create: async (qualificationData) => {
    const { name, is_active = 1, created_by } = qualificationData;
    
    const query = `
      INSERT INTO qualifications (name, is_active, created_by) 
      VALUES (?, ?, ?)
    `;
    
    const result = await db.query(query, [name, is_active, created_by]);
    return { id: result.insertId, name, is_active, created_by };
  },

  // Update qualification
  update: async (id, qualificationData) => {
    const { name, is_active, updated_by } = qualificationData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE qualifications SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...qualificationData };
  },

  // Delete qualification
  delete: async (id) => {
    const query = 'DELETE FROM qualifications WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete qualifications
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid qualification IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM qualifications WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle qualification active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE qualifications SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = Qualification;