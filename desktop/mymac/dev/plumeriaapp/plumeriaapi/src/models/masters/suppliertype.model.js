// src/models/masters/suppliertype.model.js
const db = require('../../config/database');

const SupplierType = {
  // Get all supplier types
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT st.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM supplier_types st
      LEFT JOIN users u1 ON st.created_by = u1.id
      LEFT JOIN users u2 ON st.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE st.is_active = 1';
    }
    
    query += ' ORDER BY st.id DESC';
    
    return await db.query(query);
  },

  // Get supplier type by ID
  findById: async (id) => {
    const query = `
      SELECT st.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM supplier_types st
      LEFT JOIN users u1 ON st.created_by = u1.id
      LEFT JOIN users u2 ON st.updated_by = u2.id
      WHERE st.id = ?
    `;
    
    const supplierTypes = await db.query(query, [id]);
    return supplierTypes[0];
  },

  // Get supplier type by name
  findByName: async (name) => {
    const query = 'SELECT * FROM supplier_types WHERE name = ?';
    const supplierTypes = await db.query(query, [name]);
    return supplierTypes[0];
  },

  // Create new supplier type
  create: async (supplierTypeData) => {
    const { name, description = null, is_active = 1, created_by } = supplierTypeData;
    
    const query = `
      INSERT INTO supplier_types (name, description, is_active, created_by) 
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, description, is_active, created_by]);
    return { id: result.insertId, name, description, is_active, created_by };
  },

  // Update supplier type
  update: async (id, supplierTypeData) => {
    const { name, description, is_active, updated_by } = supplierTypeData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (description !== undefined) {
      updates.push('description = ?');
      params.push(description);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE supplier_types SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...supplierTypeData };
  },

  // Delete supplier type
  delete: async (id) => {
    const query = 'DELETE FROM supplier_types WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete supplier types
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid supplier type IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM supplier_types WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle supplier type active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE supplier_types SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = SupplierType;