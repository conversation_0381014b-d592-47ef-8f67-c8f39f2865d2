// src/models/masters/projectstatus.model.js
const db = require('../../config/database');

const ProjectStatus = {
  // Get all project statuses
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT ps.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM project_statuses ps
      LEFT JOIN users u1 ON ps.created_by = u1.id
      LEFT JOIN users u2 ON ps.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE ps.is_active = 1';
    }
    
    query += ' ORDER BY ps.id DESC';
    
    return await db.query(query);
  },

  // Get project status by ID
  findById: async (id) => {
    const query = `
      SELECT ps.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM project_statuses ps
      LEFT JOIN users u1 ON ps.created_by = u1.id
      LEFT JOIN users u2 ON ps.updated_by = u2.id
      WHERE ps.id = ?
    `;
    
    const projectStatuses = await db.query(query, [id]);
    return projectStatuses[0];
  },

  // Get project status by name
  findByName: async (name) => {
    const query = 'SELECT * FROM project_statuses WHERE name = ?';
    const projectStatuses = await db.query(query, [name]);
    return projectStatuses[0];
  },

  // Create new project status
  create: async (projectStatusData) => {
    const { name, is_active = 1, created_by } = projectStatusData;
    
    const query = `
      INSERT INTO project_statuses (name, is_active, created_by) 
      VALUES (?, ?, ?)
    `;
    
    const result = await db.query(query, [name, is_active, created_by]);
    return { id: result.insertId, name, is_active, created_by };
  },

  // Update project status
  update: async (id, projectStatusData) => {
    const { name, is_active, updated_by } = projectStatusData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE project_statuses SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...projectStatusData };
  },

  // Delete project status
  delete: async (id) => {
    const query = 'DELETE FROM project_statuses WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete project statuses
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid project status IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM project_statuses WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle project status active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE project_statuses SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = ProjectStatus;