// src/models/masters/productcategory.model.js
const db = require('../../config/database');

const ProductCategory = {
  // Get all product categories
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT pc.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM product_categories pc
      LEFT JOIN users u1 ON pc.created_by = u1.id
      LEFT JOIN users u2 ON pc.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE pc.is_active = 1';
    }
    
    query += ' ORDER BY pc.id DESC';
    
    return await db.query(query);
  },

  // Get product category by ID
  findById: async (id) => {
    const query = `
      SELECT pc.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM product_categories pc
      LEFT JOIN users u1 ON pc.created_by = u1.id
      LEFT JOIN users u2 ON pc.updated_by = u2.id
      WHERE pc.id = ?
    `;
    
    const productCategories = await db.query(query, [id]);
    return productCategories[0];
  },

  // Get product category by name
  findByName: async (name) => {
    const query = 'SELECT * FROM product_categories WHERE name = ?';
    const productCategories = await db.query(query, [name]);
    return productCategories[0];
  },

  // Create new product category
  create: async (productCategoryData) => {
    const { name, description = null, is_active = 1, created_by } = productCategoryData;
    
    const query = `
      INSERT INTO product_categories (name, description, is_active, created_by) 
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, description, is_active, created_by]);
    return { id: result.insertId, name, description, is_active, created_by };
  },

  // Update product category
  update: async (id, productCategoryData) => {
    const { name, description, is_active, updated_by } = productCategoryData;
    
    const updates = [];
    const params = [];
    
    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    
    if (description !== undefined) {
      updates.push('description = ?');
      params.push(description);
    }
    
    if (is_active !== undefined) {
      updates.push('is_active = ?');
      params.push(is_active);
    }
    
    updates.push('updated_by = ?');
    params.push(updated_by);
    
    const query = `UPDATE product_categories SET ${updates.join(', ')} WHERE id = ?`;
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...productCategoryData };
  },

  // Delete product category
  delete: async (id) => {
    const query = 'DELETE FROM product_categories WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete product categories
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid product category IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM product_categories WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle product category active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE product_categories SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = ProductCategory;