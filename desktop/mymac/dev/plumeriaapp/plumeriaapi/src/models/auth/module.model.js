const db = require('../../config/database');

const Module = {
  // Get all modules
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT m.*, u.username as created_by_username
      FROM modules m
      LEFT JOIN users u ON m.created_by = u.id
    `;
    if (!includeInactive) {
      query += ' WHERE m.is_active = 1';
    }
    return await db.query(query);
  },

  // Get module by ID
  findById: async (id) => {
    const query = `
      SELECT m.*, u.username as created_by_username
      FROM modules m
      LEFT JOIN users u ON m.created_by = u.id
      WHERE m.id = ?
    `;
    const modules = await db.query(query, [id]);
    return modules[0];
  },

  // Get module by name
  findByName: async (name) => {
    const query = 'SELECT * FROM modules WHERE name = ?';
    const modules = await db.query(query, [name]);
    return modules[0];
  },

  // Create new module
  create: async (moduleData) => {
    const { name, description, is_active = 1, created_by } = moduleData;
    const query = 'INSERT INTO modules (name, description, is_active, created_by) VALUES (?, ?, ?, ?)';
    const result = await db.query(query, [name, description, is_active, created_by]);
    return { id: result.insertId, name, description, is_active, created_by };
  },

  // Update module
  update: async (id, moduleData) => {
    const { name, description, is_active } = moduleData;
    let query = 'UPDATE modules SET ';
    const params = [];

    if (name) {
      query += 'name = ?, ';
      params.push(name);
    }

    if (description !== undefined) {
      query += 'description = ?, ';
      params.push(description);
    }

    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }

    // Remove trailing comma and space
    query = query.slice(0, -2);

    query += ' WHERE id = ?';
    params.push(id);

    await db.query(query, params);

    return { id, ...moduleData };
  },

  // Delete module
  delete: async (id) => {
    const query = 'DELETE FROM modules WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Toggle module active status
  toggleActive: async (id, isActive) => {
    const query = 'UPDATE modules SET is_active = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, id]);
    return { id, is_active: isActive };
  },

  // Get module permissions
  getModulePermissions: async (moduleId) => {
    const query = `
      SELECT p.* FROM permissions p
      JOIN role_module_permissions mp ON p.id = mp.permission_id
      WHERE mp.module_id = ?
    `;
    return await db.query(query, [moduleId]);
  },

  // Add permission to module
  addPermission: async (moduleId, permissionId) => {
    const query = 'INSERT INTO role_module_permissions (module_id, permission_id) VALUES (?, ?)';
    return await db.query(query, [moduleId, permissionId]);
  },

  // Remove permission from module
  removePermission: async (moduleId, permissionId) => {
    const query = 'DELETE FROM role_module_permissions WHERE module_id = ? AND permission_id = ?';
    return await db.query(query, [moduleId, permissionId]);
  },

  // Bulk delete modules
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid module IDs for bulk deletion');
    }

    // Create placeholders for the IN clause
    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM modules WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  }
};

module.exports = Module;

