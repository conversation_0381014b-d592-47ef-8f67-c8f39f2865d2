const db = require('../../config/database');

const RoleModulePermission = {
  // Get all role-module-permissions
  findAll: async () => {
    const query = `
      SELECT 
        rmp.role_id, 
        rmp.module_id, 
        rmp.permission_id,
        r.name as role_name,
        m.name as module_name,
        p.name as permission_name
      FROM 
        role_module_permissions rmp
      JOIN 
        roles r ON rmp.role_id = r.id
      JOIN 
        modules m ON rmp.module_id = m.id
      JOIN 
        permissions p ON rmp.permission_id = p.id
    `;
    return await db.query(query);
  },
  
  // Get all permissions for a specific role-module combination
  findByRoleAndModule: async (roleId, moduleId) => {
    const query = `
      SELECT 
        rmp.role_id, 
        rmp.module_id, 
        rmp.permission_id,
        r.name as role_name,
        m.name as module_name,
        p.name as permission_name,
        p.description as permission_description
      FROM 
        role_module_permissions rmp
      JOIN 
        roles r ON rmp.role_id = r.id
      JOIN 
        modules m ON rmp.module_id = m.id
      JOIN 
        permissions p ON rmp.permission_id = p.id
      WHERE 
        rmp.role_id = ? AND rmp.module_id = ?
    `;
    return await db.query(query, [roleId, moduleId]);
  },
  
  // Get all module permissions for a role
  findByRole: async (roleId) => {
    const query = `
      SELECT 
        rmp.role_id, 
        rmp.module_id, 
        rmp.permission_id,
        r.name as role_name,
        m.name as module_name,
        p.name as permission_name
      FROM 
        role_module_permissions rmp
      JOIN 
        roles r ON rmp.role_id = r.id
      JOIN 
        modules m ON rmp.module_id = m.id
      JOIN 
        permissions p ON rmp.permission_id = p.id
      WHERE 
        rmp.role_id = ?
    `;
    return await db.query(query, [roleId]);
  },
  
  // Check if a specific role-module-permission entry exists
  exists: async (roleId, moduleId, permissionId) => {
    const query = `
      SELECT 1 FROM role_module_permissions
      WHERE role_id = ? AND module_id = ? AND permission_id = ?
    `;
    const result = await db.query(query, [roleId, moduleId, permissionId]);
    return result.length > 0;
  },
  
  // Add a new role-module-permission entry
  create: async (roleId, moduleId, permissionId) => {
    const query = `
      INSERT INTO role_module_permissions (role_id, module_id, permission_id)
      VALUES (?, ?, ?)
    `;
    await db.query(query, [roleId, moduleId, permissionId]);
    return { roleId, moduleId, permissionId };
  },
  
  // Remove a role-module-permission entry
  delete: async (roleId, moduleId, permissionId) => {
    const query = `
      DELETE FROM role_module_permissions 
      WHERE role_id = ? AND module_id = ? AND permission_id = ?
    `;
    return await db.query(query, [roleId, moduleId, permissionId]);
  },
  
  // Remove all permissions for a role-module combination
  deleteByRoleAndModule: async (roleId, moduleId) => {
    const query = `
      DELETE FROM role_module_permissions 
      WHERE role_id = ? AND module_id = ?
    `;
    return await db.query(query, [roleId, moduleId]);
  },
  
  // Get all module permissions for a user
  getUserModulePermissions: async (userId) => {
    // First get user's active roles
    const userRolesQuery = `
      SELECT r.id 
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND r.is_active = 1
    `;
    const userRoles = await db.query(userRolesQuery, [userId]);
    const activeRoleIds = userRoles.map(role => role.id);
    
    const modulePermissions = {};
    
    if (activeRoleIds.length > 0) {
      const roleIdsPlaceholder = activeRoleIds.map(() => '?').join(',');
      const permissionsQuery = `
        SELECT 
          m.name AS module_name, 
          p.name AS permission_name
        FROM 
          role_module_permissions rmp
        JOIN 
          modules m ON rmp.module_id = m.id
        JOIN 
          permissions p ON rmp.permission_id = p.id
        WHERE 
          rmp.role_id IN (${roleIdsPlaceholder})
          AND m.is_active = 1
      `;
      
      const permissions = await db.query(permissionsQuery, activeRoleIds);
      
      // Group by module
      permissions.forEach(p => {
        if (!modulePermissions[p.module_name]) {
          modulePermissions[p.module_name] = [];
        }
        
        if (!modulePermissions[p.module_name].includes(p.permission_name)) {
          modulePermissions[p.module_name].push(p.permission_name);
        }
      });
    }
    
    return modulePermissions;
  },
  
  // Get module permissions for a specific module
  getModulePermissions: async (moduleId) => {
    const query = `
      SELECT 
        p.id,
        p.name,
        p.description
      FROM 
        permissions p
      JOIN 
        role_module_permissions rmp ON p.id = rmp.permission_id
      WHERE 
        rmp.module_id = ?
      GROUP BY 
        p.id, p.name, p.description
    `;
    return await db.query(query, [moduleId]);
  },


  // Add to role_module_permission.model.js
checkUserPermission: async (userId, moduleName, permissionName) => {
  try {
    // Get user's active roles
    const userRolesQuery = `
      SELECT r.id 
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND r.is_active = 1
    `;
    const userRoles = await db.query(userRolesQuery, [userId]);
    const activeRoleIds = userRoles.map(role => role.id);
    
    if (activeRoleIds.length === 0) {
      return false;
    }
    
    // Get the module by name
    const moduleQuery = 'SELECT * FROM modules WHERE name = ?';
    const modules = await db.query(moduleQuery, [moduleName]);
    const moduleObj = modules[0];
    
    if (!moduleObj) {
      return false;
    }
    
    // Map permission string to permission ID
    const permissionMap = {
      'create': 1,
      'read': 2,
      'update': 3,
      'delete': 4
    };
    
    const permissionId = permissionMap[permissionName];
    if (!permissionId) {
      return false;
    }
    
    // Check role module permission
    const placeholders = activeRoleIds.map(() => '?').join(',');
    const permissionQuery = `
      SELECT 1 FROM role_module_permissions
      WHERE role_id IN (${placeholders})
      AND module_id = ?
      AND permission_id = ?
      LIMIT 1
    `;
    
    const params = [...activeRoleIds, moduleObj.id, permissionId];
    const result = await db.query(permissionQuery, params);
    
    return result.length > 0;
  } catch (error) {
    console.error('Error checking user permission:', error);
    return false;
  }
}
};

module.exports = RoleModulePermission;