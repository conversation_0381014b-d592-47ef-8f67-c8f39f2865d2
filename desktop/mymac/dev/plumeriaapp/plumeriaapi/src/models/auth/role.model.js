const db = require('../../config/database');

const Role = {
  // Get all roles
  findAll: async (includeInactive = false) => {
    let query = 'SELECT * FROM roles';
    if (!includeInactive) {
      query += ' WHERE is_active = 1';
    }
    return await db.query(query);
  },

  // Get all roles with creator information
  findAllWithCreator: async (includeInactive = false) => {
    let query = `
      SELECT r.*, u.name as creator_name 
      FROM roles r
      LEFT JOIN users u ON r.created_by = u.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE r.is_active = 1';
    }
    
    return await db.query(query);
  },

  // Get role by ID
  findById: async (id) => {
    const query = 'SELECT * FROM roles WHERE id = ?';
    const roles = await db.query(query, [id]);
    return roles[0];
  },

  // Get role by ID with creator information
  findByIdWithCreator: async (id) => {
    const query = `
      SELECT r.*, u.name as creator_name 
      FROM roles r
      LEFT JOIN users u ON r.created_by = u.id
      WHERE r.id = ?
    `;
    const roles = await db.query(query, [id]);
    return roles[0];
  },

  // Get role by name
  findByName: async (name) => {
    console.log('Finding role by name:', name);
    const query = 'SELECT * FROM roles WHERE name = ?';
    const roles = await db.query(query, [name]);
    console.log('Found role:', roles[0]);
    return roles[0];
  },

  // Create new role
  create: async (roleData) => {
    const { name, description, is_active = 1, created_by } = roleData;
    const query = 'INSERT INTO roles (name, description, is_active, created_by) VALUES (?, ?, ?, ?)';
    const result = await db.query(query, [name, description, is_active, created_by]);
    return { id: result.insertId, name, description, is_active, created_by };
  },

  // Update role
  update: async (id, roleData) => {
    const { name, description, is_active } = roleData;
    let query = 'UPDATE roles SET ';
    const params = [];
    
    if (name) {
      query += 'name = ?, ';
      params.push(name);
    }
    
    if (description !== undefined) {
      query += 'description = ?, ';
      params.push(description);
    }
    
    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }
    
    // Remove trailing comma and space
    query = query.slice(0, -2);
    
    query += ' WHERE id = ?';
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...roleData };
  },

  // Delete role
  delete: async (id) => {
    const query = 'DELETE FROM roles WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Toggle role active status
  toggleActive: async (id, isActive) => {
    const query = 'UPDATE roles SET is_active = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, id]);
    return { id, is_active: isActive };
  },

  // Get permissions for a role
  getRolePermissions: async (roleId) => {
    const query = `
      SELECT p.* FROM permissions p
      JOIN role_module_permissions rmp ON p.id = rmp.permission_id
      WHERE rmp.role_id = ?
    `;
    return await db.query(query, [roleId]);
  },
  // Assign permission to role
  assignPermission: async (roleId, permissionId) => {
    const query = 'INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)';
    return await db.query(query, [roleId, permissionId]);
  },

  // Remove permission from role
  removePermission: async (roleId, permissionId) => {
    const query = 'DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?';
    return await db.query(query, [roleId, permissionId]);
  },

  // Get users with a specific role
  getUsersByRole: async (roleId, includeInactiveRoles = false) => {
    let query = `
      SELECT u.* FROM users u
      JOIN user_roles ur ON u.id = ur.user_id
      JOIN roles r ON r.id = ur.role_id
      WHERE ur.role_id = ?
    `;
    
    if (!includeInactiveRoles) {
      query += ' AND r.is_active = 1';
    }
    
    return await db.query(query, [roleId]);
  },

  // Assign role to user
  assignToUser: async (userId, roleId) => {
    console.log(`Assigning role ${roleId} to user ${userId}`);
    try {
      const query = 'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)';
      const result = await db.query(query, [userId, roleId]);
      console.log('Assignment successful:', result);
      return result;
    } catch (error) {
      console.error('Error assigning role to user:', error);
      throw error;
    }
  },

  // Remove role from user
  removeFromUser: async (userId, roleId) => {
    console.log(`Removing role ${roleId} from user ${userId}`);
    const query = 'DELETE FROM user_roles WHERE user_id = ? AND role_id = ?';
    return await db.query(query, [userId, roleId]);
  }
};

module.exports = Role;