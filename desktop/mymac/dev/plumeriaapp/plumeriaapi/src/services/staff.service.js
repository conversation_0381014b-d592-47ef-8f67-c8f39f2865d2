const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

/**
 * Staff Service
 * Handles business logic and file operations for staff management
 * @class StaffService
 */
const StaffService = {
  /**
   * Validate staff data
   * @param {Object} staffData - Staff data to validate
   * @param {boolean} isUpdate - Whether this is an update operation
   * @returns {Object} Validation result
   */
  validateStaffData: (staffData, isUpdate = false) => {
    const errors = [];

    // Required fields for creation
    if (!isUpdate) {
      if (!staffData.staff_name || staffData.staff_name.trim().length < 2) {
        errors.push('Staff name is required and must be at least 2 characters');
      }

      if (!staffData.staff_mobile || !StaffService.isValidMobile(staffData.staff_mobile)) {
        errors.push('Valid mobile number is required');
      }

      if (!staffData.staff_email || !StaffService.isValidEmail(staffData.staff_email)) {
        errors.push('Valid email address is required');
      }

      if (!staffData.gender || !['Male', 'Female', 'Other'].includes(staffData.gender)) {
        errors.push('Valid gender is required (Male, Female, Other)');
      }

      if (!staffData.date_of_birth || !StaffService.isValidDate(staffData.date_of_birth)) {
        errors.push('Valid date of birth is required');
      }

      if (!staffData.marital_status || !['Single', 'Married', 'Divorced', 'Widowed'].includes(staffData.marital_status)) {
        errors.push('Valid marital status is required');
      }

      if (!staffData.joining_date || !StaffService.isValidDate(staffData.joining_date)) {
        errors.push('Valid joining date is required');
      }

      if (!staffData.emergency_contact_name || staffData.emergency_contact_name.trim().length < 2) {
        errors.push('Emergency contact name is required');
      }

      if (!staffData.emergency_contact_phone || !StaffService.isValidMobile(staffData.emergency_contact_phone)) {
        errors.push('Valid emergency contact phone is required');
      }

      if (!staffData.emergency_contact_relation || staffData.emergency_contact_relation.trim().length < 2) {
        errors.push('Emergency contact relation is required');
      }

      if (!staffData.designation_id || isNaN(parseInt(staffData.designation_id))) {
        errors.push('Valid designation is required');
      }

      if (!staffData.employment_type_id || isNaN(parseInt(staffData.employment_type_id))) {
        errors.push('Valid employment type is required');
      }

      if (!staffData.department_id || isNaN(parseInt(staffData.department_id))) {
        errors.push('Valid department is required');
      }

      if (!staffData.staff_address || staffData.staff_address.trim().length < 10) {
        errors.push('Staff address is required and must be at least 10 characters');
      }

      if (!staffData.locality_id || isNaN(parseInt(staffData.locality_id))) {
        errors.push('Valid locality is required');
      }

      if (!staffData.city_id || isNaN(parseInt(staffData.city_id))) {
        errors.push('Valid city is required');
      }

      if (!staffData.state_id || isNaN(parseInt(staffData.state_id))) {
        errors.push('Valid state is required');
      }

      if (!staffData.pincode || !StaffService.isValidPincode(staffData.pincode)) {
        errors.push('Valid pincode is required');
      }
    }

    // Validate optional fields if provided
    if (staffData.staff_email && !StaffService.isValidEmail(staffData.staff_email)) {
      errors.push('Invalid email format');
    }

    if (staffData.staff_mobile && !StaffService.isValidMobile(staffData.staff_mobile)) {
      errors.push('Invalid mobile number format');
    }

    if (staffData.aadhaar_number && !StaffService.isValidAadhaar(staffData.aadhaar_number)) {
      errors.push('Invalid Aadhaar number format (12 digits required)');
    }

    if (staffData.pan_number && !StaffService.isValidPAN(staffData.pan_number)) {
      errors.push('Invalid PAN number format (********** format required)');
    }

    if (staffData.ifsc_code && !StaffService.isValidIFSC(staffData.ifsc_code)) {
      errors.push('Invalid IFSC code format');
    }

    if (staffData.date_of_birth && !StaffService.isValidAge(staffData.date_of_birth)) {
      errors.push('Staff must be at least 18 years old');
    }

    if (staffData.salary_amount && (isNaN(parseFloat(staffData.salary_amount)) || parseFloat(staffData.salary_amount) < 0)) {
      errors.push('Salary amount must be a valid positive number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} Is valid email
   */
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate mobile number format
   * @param {string} mobile - Mobile number to validate
   * @returns {boolean} Is valid mobile
   */
  isValidMobile: (mobile) => {
    const mobileRegex = /^[6-9]\d{9}$/;
    return mobileRegex.test(mobile.replace(/\D/g, ''));
  },

  /**
   * Validate Aadhaar number format
   * @param {string} aadhaar - Aadhaar number to validate
   * @returns {boolean} Is valid Aadhaar
   */
  isValidAadhaar: (aadhaar) => {
    const aadhaarRegex = /^\d{12}$/;
    return aadhaarRegex.test(aadhaar.replace(/\D/g, ''));
  },

  /**
   * Validate PAN number format
   * @param {string} pan - PAN number to validate
   * @returns {boolean} Is valid PAN
   */
  isValidPAN: (pan) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan.toUpperCase());
  },

  /**
   * Validate IFSC code format
   * @param {string} ifsc - IFSC code to validate
   * @returns {boolean} Is valid IFSC
   */
  isValidIFSC: (ifsc) => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscRegex.test(ifsc.toUpperCase());
  },

  /**
   * Validate pincode format
   * @param {string} pincode - Pincode to validate
   * @returns {boolean} Is valid pincode
   */
  isValidPincode: (pincode) => {
    const pincodeRegex = /^\d{6}$/;
    return pincodeRegex.test(pincode);
  },

  /**
   * Validate date format
   * @param {string} dateString - Date string to validate
   * @returns {boolean} Is valid date
   */
  isValidDate: (dateString) => {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  },

  /**
   * Validate age (must be at least 18)
   * @param {string} dateOfBirth - Date of birth
   * @returns {boolean} Is valid age
   */
  isValidAge: (dateOfBirth) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1 >= 18;
    }
    
    return age >= 18;
  },

  /**
   * Handle profile picture upload
   * @param {Object} file - Uploaded file object
   * @param {Object} staff - Staff record
   * @returns {Promise<string>} File path
   */
  handleProfilePictureUpload: async (file, staff) => {
    try {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.mimetype)) {
        throw new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed');
      }

      // Validate file size (2MB max)
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        throw new Error('File size too large. Maximum size is 2MB');
      }

      // Create uploads directory if it doesn't exist
      const uploadsDir = path.join(__dirname, '../public/uploads/staff');
      await fs.mkdir(uploadsDir, { recursive: true });

      // Generate unique filename
      const fileExtension = path.extname(file.originalname);
      const fileName = `${staff.id}_${uuidv4()}${fileExtension}`;
      const filePath = path.join(uploadsDir, fileName);

      // Remove old profile picture if exists
      if (staff.profile_picture) {
        await StaffService.removeProfilePictureFile(staff.profile_picture);
      }

      // Save new file
      await fs.writeFile(filePath, file.buffer);

      // Return relative path for database storage
      return `uploads/staff/${fileName}`;
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
  },

  /**
   * Remove profile picture file
   * @param {string} filePath - File path to remove
   * @returns {Promise<void>}
   */
  removeProfilePictureFile: async (filePath) => {
    try {
      if (!filePath) return;

      const fullPath = path.join(__dirname, '../public', filePath);
      
      // Check if file exists before attempting to delete
      try {
        await fs.access(fullPath);
        await fs.unlink(fullPath);
      } catch (error) {
        // File doesn't exist, which is fine
        console.log(`Profile picture file not found: ${fullPath}`);
      }
    } catch (error) {
      console.error('Error removing profile picture file:', error);
      // Don't throw error as this is not critical
    }
  }
};

module.exports = StaffService;
