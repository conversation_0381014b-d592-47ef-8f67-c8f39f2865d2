const Staff = require('../models/staff.model');
const StaffService = require('../services/staff.service');

/**
 * Staff Controller - Following BloodGroup pattern exactly
 */

// Get all staff
const getAllStaff = async (req, res) => {
  try {
    console.log('=== getAllStaff - Following BloodGroup Pattern ===');
    console.log('req.query:', req.query);

    const {
      page = 1,
      limit = 10,
      search,
      departmentId,
      designationId,
      includeInactive = false
    } = req.query;

    console.log('Extracted values:', { page, limit, search, departmentId, designationId, includeInactive });

    // If no filters are provided, use the simple findAll like BloodGroup
    if (!search && !departmentId && !designationId && page == 1 && limit == 10) {
      console.log('Using simple findAll - no filters');
      const staff = await Staff.findAll(includeInactive === 'true');

      return res.json({
        success: true,
        data: staff,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalCount: staff.length,
          limit: staff.length,
          hasNext: false,
          hasPrev: false
        }
      });
    }

    // For filtered/paginated requests, use the new method
    console.log('Using findAllWithFilters - has filters or pagination');

    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 10));
    const offset = (pageNum - 1) * limitNum;

    const options = {
      includeInactive: includeInactive === 'true',
      limit: limitNum,
      offset: offset
    };

    // Only add filters if they have values
    if (departmentId && departmentId !== '' && !isNaN(parseInt(departmentId))) {
      options.departmentId = parseInt(departmentId);
    }

    if (designationId && designationId !== '' && !isNaN(parseInt(designationId))) {
      options.designationId = parseInt(designationId);
    }

    if (search && search.trim() !== '') {
      options.search = search.trim();
    }

    console.log('Final options:', JSON.stringify(options, null, 2));

    // Get staff data and count
    const [staff, totalCount] = await Promise.all([
      Staff.findAllWithFilters(options),
      Staff.getCount(options)
    ]);

    const totalPages = Math.ceil(totalCount / limitNum);

    return res.json({
      success: true,
      data: staff,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        limit: limitNum,
        hasNext: pageNum < totalPages,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error fetching staff:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff records',
      error: error.message
    });
  }
};

// Get staff by ID
const getStaffById = async (req, res) => {
  try {
    const { id } = req.params;
    const staff = await Staff.findById(id);

    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    return res.json({
      success: true,
      data: staff
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff member',
      error: error.message
    });
  }
};

// Create a new staff member
const createStaff = async (req, res) => {
  try {
    const staffData = req.body;

    if (!staffData.staff_name) {
      return res.status(400).json({
        success: false,
        message: 'Staff name is required'
      });
    }

    if (!staffData.staff_email) {
      return res.status(400).json({
        success: false,
        message: 'Staff email is required'
      });
    }

    if (!staffData.staff_mobile) {
      return res.status(400).json({
        success: false,
        message: 'Staff mobile is required'
      });
    }

    // Check if staff email already exists
    const existingStaffWithEmail = await Staff.findByEmail(staffData.staff_email);
    if (existingStaffWithEmail) {
      return res.status(409).json({
        success: false,
        message: 'Staff member with this email already exists'
      });
    }

    // Check if staff mobile already exists
    const existingStaffWithMobile = await Staff.findByMobile(staffData.staff_mobile);
    if (existingStaffWithMobile) {
      return res.status(409).json({
        success: false,
        message: 'Staff member with this mobile already exists'
      });
    }

    // Create the staff with the authenticated user's ID
    const staff = await Staff.create({
      ...staffData,
      created_by: req.user.id
    });

    return res.status(201).json({
      success: true,
      message: 'Staff member created successfully',
      data: staff
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating staff member',
      error: error.message
    });
  }
};

// Update a staff member
const updateStaff = async (req, res) => {
  try {
    const { id } = req.params;
    const staffData = req.body;

    // Check if staff exists
    const staff = await Staff.findById(id);
    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Check if email is being changed and if it already exists
    if (staffData.staff_email && staffData.staff_email !== staff.staff_email) {
      const existingStaff = await Staff.findByEmail(staffData.staff_email);
      if (existingStaff) {
        return res.status(409).json({
          success: false,
          message: 'Staff member with this email already exists'
        });
      }
    }

    // Check if mobile is being changed and if it already exists
    if (staffData.staff_mobile && staffData.staff_mobile !== staff.staff_mobile) {
      const existingStaff = await Staff.findByMobile(staffData.staff_mobile);
      if (existingStaff) {
        return res.status(409).json({
          success: false,
          message: 'Staff member with this mobile already exists'
        });
      }
    }

    // Update the staff with the authenticated user's ID as the updater
    const updatedStaff = await Staff.update(id, {
      ...staffData,
      updated_by: req.user.id
    });

    return res.json({
      success: true,
      message: 'Staff member updated successfully',
      data: updatedStaff
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating staff member',
      error: error.message
    });
  }
};

// Delete a staff member
const deleteStaff = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if staff exists
    const staff = await Staff.findById(id);
    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Delete the staff
    await Staff.delete(id);

    return res.json({
      success: true,
      message: 'Staff member deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting staff member',
      error: error.message
    });
  }
};

// Toggle staff active status
const toggleStaffStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }

    // Check if staff exists
    const staff = await Staff.findById(id);
    if (!staff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Toggle the status
    const updatedStaff = await Staff.toggleActive(id, Boolean(is_active), req.user.id);

    return res.json({
      success: true,
      message: `Staff member ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedStaff
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling staff status',
      error: error.message
    });
  }
};

// Bulk delete staff
const bulkDeleteStaff = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of staff IDs is required'
      });
    }

    // Perform bulk delete
    await Staff.bulkDelete(ids);

    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} staff members`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

// Get staff by department
const getStaffByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;
    const { includeInactive = false } = req.query;

    if (!departmentId || isNaN(parseInt(departmentId))) {
      return res.status(400).json({
        success: false,
        message: 'Valid department ID is required'
      });
    }

    const staff = await Staff.findByDepartment(
      parseInt(departmentId),
      includeInactive === 'true'
    );

    return res.json({
      success: true,
      data: staff
    });
  } catch (error) {
    console.error('Error fetching staff by department:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff by department',
      error: error.message
    });
  }
};

// Upload profile picture
const uploadProfilePicture = async (req, res) => {
  try {
    const { id } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Profile picture file is required'
      });
    }

    // Check if staff exists
    const existingStaff = await Staff.findById(id);
    if (!existingStaff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Process file upload using service
    const profilePicturePath = await StaffService.handleProfilePictureUpload(req.file, existingStaff);

    // Update staff record with new profile picture
    const result = await Staff.updateProfilePicture(id, profilePicturePath, req.user.id);

    return res.json({
      success: true,
      message: 'Profile picture uploaded successfully',
      data: result
    });
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    return res.status(500).json({
      success: false,
      message: 'Error uploading profile picture',
      error: error.message
    });
  }
};

// Remove profile picture
const removeProfilePicture = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if staff exists
    const existingStaff = await Staff.findById(id);
    if (!existingStaff) {
      return res.status(404).json({
        success: false,
        message: 'Staff member not found'
      });
    }

    // Remove profile picture file if exists
    if (existingStaff.profile_picture) {
      await StaffService.removeProfilePictureFile(existingStaff.profile_picture);
    }

    // Update staff record
    const result = await Staff.removeProfilePicture(id, req.user.id);

    return res.json({
      success: true,
      message: 'Profile picture removed successfully',
      data: result
    });
  } catch (error) {
    console.error('Error removing profile picture:', error);
    return res.status(500).json({
      success: false,
      message: 'Error removing profile picture',
      error: error.message
    });
  }
};

// Bulk update status
const bulkUpdateStatus = async (req, res) => {
  try {
    const { ids, is_active } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of staff IDs is required'
      });
    }

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }

    // Perform bulk status update
    await Staff.bulkUpdateStatus(ids, Boolean(is_active), req.user.id);

    return res.json({
      success: true,
      message: `Successfully ${is_active ? 'activated' : 'deactivated'} ${ids.length} staff members`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk status update',
      error: error.message
    });
  }
};

// Get staff by IDs
const getStaffByIds = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of staff IDs is required'
      });
    }

    const staff = await Staff.findByIds(ids);

    return res.json({
      success: true,
      data: staff
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff by IDs',
      error: error.message
    });
  }
};

// Search staff
const searchStaff = async (req, res) => {
  try {
    const { q, limit = 10 } = req.query;

    if (!q || q.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const staff = await Staff.search(q.trim(), parseInt(limit));

    return res.json({
      success: true,
      data: staff
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error searching staff',
      error: error.message
    });
  }
};

// Get staff statistics
const getStaffStats = async (_req, res) => {
  try {
    const stats = await Staff.getStats();

    return res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff statistics',
      error: error.message
    });
  }
};

// Export staff data
const exportStaff = async (req, res) => {
  try {
    const { format = 'csv', includeInactive = false } = req.query;

    if (!['csv', 'excel'].includes(format)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid export format. Use csv or excel'
      });
    }

    const staff = await Staff.findAll(includeInactive === 'true');

    if (format === 'csv') {
      // Generate CSV
      const csv = Staff.generateCSV(staff);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=staff_export.csv');
      return res.send(csv);
    } else {
      // For Excel export, you would need additional libraries like xlsx
      return res.status(501).json({
        success: false,
        message: 'Excel export not implemented yet'
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error exporting staff data',
      error: error.message
    });
  }
};

module.exports = {
  getAllStaff,
  getStaffById,
  createStaff,
  updateStaff,
  deleteStaff,
  toggleStaffStatus,
  bulkDeleteStaff,
  bulkUpdateStatus,
  getStaffByIds,
  searchStaff,
  getStaffStats,
  exportStaff,
  getStaffByDepartment,
  uploadProfilePicture,
  removeProfilePicture
};