// src/controllers/masters/productsubcategory.controller.js
const ProductSubcategory = require('../../models/masters/productsubcategory.model');
const ProductCategory = require('../../models/masters/productcategory.model');

// Get all product subcategories
const getAllProductSubcategories = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const categoryId = req.query.categoryId;
    
    let productSubcategories;
    
    if (categoryId) {
      // If category ID is provided, filter by category
      productSubcategories = await ProductSubcategory.findByCategory(categoryId, includeInactive);
    } else {
      // Otherwise, get all subcategories
      productSubcategories = await ProductSubcategory.findAll(includeInactive);
    }
    
    return res.json({
      success: true,
      data: productSubcategories
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching product subcategories',
      error: error.message
    });
  }
};

// Get product subcategory by ID
const getProductSubcategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    const productSubcategory = await ProductSubcategory.findById(id);
    
    if (!productSubcategory) {
      return res.status(404).json({
        success: false,
        message: 'Product subcategory not found'
      });
    }
    
    return res.json({
      success: true,
      data: productSubcategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching product subcategory',
      error: error.message
    });
  }
};

// Create a new product subcategory
const createProductSubcategory = async (req, res) => {
  try {
    const { name, category_id, description, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Product subcategory name is required'
      });
    }
    
    if (!category_id) {
      return res.status(400).json({
        success: false,
        message: 'Category ID is required'
      });
    }
    
    // Check if the category exists
    const category = await ProductCategory.findById(category_id);
    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Product category not found'
      });
    }
    
    // Check if subcategory name already exists within this category
    const existingSubcategory = await ProductSubcategory.findByNameAndCategory(name, category_id);
    if (existingSubcategory) {
      return res.status(409).json({
        success: false,
        message: 'Product subcategory name already exists in this category'
      });
    }
    
    // Create the product subcategory with the authenticated user's ID
    const productSubcategory = await ProductSubcategory.create({
      name,
      category_id,
      description,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Product subcategory created successfully',
      data: productSubcategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating product subcategory',
      error: error.message
    });
  }
};

// Update a product subcategory
const updateProductSubcategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, category_id, description, is_active } = req.body;
    
    // Check if product subcategory exists
    const productSubcategory = await ProductSubcategory.findById(id);
    if (!productSubcategory) {
      return res.status(404).json({
        success: false,
        message: 'Product subcategory not found'
      });
    }
    
    // If category_id is being updated, check if the new category exists
    if (category_id && category_id !== productSubcategory.category_id) {
      const category = await ProductCategory.findById(category_id);
      if (!category) {
        return res.status(404).json({
          success: false,
          message: 'Product category not found'
        });
      }
    }
    
    // Check if name is being changed and if it already exists within the category
    const categoryIdToCheck = category_id || productSubcategory.category_id;
    if (name && name !== productSubcategory.name) {
      const existingSubcategory = await ProductSubcategory.findByNameAndCategory(name, categoryIdToCheck);
      if (existingSubcategory) {
        return res.status(409).json({
          success: false,
          message: 'Product subcategory name already exists in this category'
        });
      }
    }
    
    // Update the product subcategory with the authenticated user's ID as the updater
    const updatedProductSubcategory = await ProductSubcategory.update(id, {
      name,
      category_id,
      description,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Product subcategory updated successfully',
      data: updatedProductSubcategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating product subcategory',
      error: error.message
    });
  }
};

// Delete a product subcategory
const deleteProductSubcategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if product subcategory exists
    const productSubcategory = await ProductSubcategory.findById(id);
    if (!productSubcategory) {
      return res.status(404).json({
        success: false,
        message: 'Product subcategory not found'
      });
    }
    
    // Delete the product subcategory
    await ProductSubcategory.delete(id);
    
    return res.json({
      success: true,
      message: 'Product subcategory deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting product subcategory',
      error: error.message
    });
  }
};

// Toggle product subcategory active status
const toggleProductSubcategoryStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if product subcategory exists
    const productSubcategory = await ProductSubcategory.findById(id);
    if (!productSubcategory) {
      return res.status(404).json({
        success: false,
        message: 'Product subcategory not found'
      });
    }
    
    // Toggle the status
    const updatedProductSubcategory = await ProductSubcategory.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Product subcategory ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedProductSubcategory
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling product subcategory status',
      error: error.message
    });
  }
};

// Bulk delete product subcategories
const bulkDeleteProductSubcategories = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of product subcategory IDs is required'
      });
    }
    
    // Perform bulk delete
    await ProductSubcategory.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} product subcategories`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllProductSubcategories,
  getProductSubcategoryById,
  createProductSubcategory,
  updateProductSubcategory,
  deleteProductSubcategory,
  toggleProductSubcategoryStatus,
  bulkDeleteProductSubcategories
};