// src/controllers/masters/projecttype.controller.js
const ProjectType = require('../../models/masters/projecttype.model');

// Get all project types
const getAllProjectTypes = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const projectTypes = await ProjectType.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: projectTypes
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching project types',
      error: error.message
    });
  }
};

// Get project type by ID
const getProjectTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const projectType = await ProjectType.findById(id);
    
    if (!projectType) {
      return res.status(404).json({
        success: false,
        message: 'Project type not found'
      });
    }
    
    return res.json({
      success: true,
      data: projectType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching project type',
      error: error.message
    });
  }
};

// Create a new project type
const createProjectType = async (req, res) => {
  try {
    const { name, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Project type name is required'
      });
    }
    
    // Check if project type name already exists
    const existingProjectTypeWithName = await ProjectType.findByName(name);
    if (existingProjectTypeWithName) {
      return res.status(409).json({
        success: false,
        message: 'Project type name already exists'
      });
    }
    
    // Create the project type with the authenticated user's ID
    const projectType = await ProjectType.create({
      name,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Project type created successfully',
      data: projectType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating project type',
      error: error.message
    });
  }
};

// Update a project type
const updateProjectType = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, is_active } = req.body;
    
    // Check if project type exists
    const projectType = await ProjectType.findById(id);
    if (!projectType) {
      return res.status(404).json({
        success: false,
        message: 'Project type not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== projectType.name) {
      const existingProjectType = await ProjectType.findByName(name);
      if (existingProjectType) {
        return res.status(409).json({
          success: false,
          message: 'Project type name already exists'
        });
      }
    }
    
    // Update the project type with the authenticated user's ID as the updater
    const updatedProjectType = await ProjectType.update(id, {
      name,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Project type updated successfully',
      data: updatedProjectType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating project type',
      error: error.message
    });
  }
};

// Delete a project type
const deleteProjectType = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if project type exists
    const projectType = await ProjectType.findById(id);
    if (!projectType) {
      return res.status(404).json({
        success: false,
        message: 'Project type not found'
      });
    }
    
    // Delete the project type
    await ProjectType.delete(id);
    
    return res.json({
      success: true,
      message: 'Project type deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting project type',
      error: error.message
    });
  }
};

// Toggle project type active status
const toggleProjectTypeStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if project type exists
    const projectType = await ProjectType.findById(id);
    if (!projectType) {
      return res.status(404).json({
        success: false,
        message: 'Project type not found'
      });
    }
    
    // Toggle the status
    const updatedProjectType = await ProjectType.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Project type ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedProjectType
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling project type status',
      error: error.message
    });
  }
};

// Bulk delete project types
const bulkDeleteProjectTypes = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of project type IDs is required'
      });
    }
    
    // Perform bulk delete
    await ProjectType.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} project types`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllProjectTypes,
  getProjectTypeById,
  createProjectType,
  updateProjectType,
  deleteProjectType,
  toggleProjectTypeStatus,
  bulkDeleteProjectTypes
};