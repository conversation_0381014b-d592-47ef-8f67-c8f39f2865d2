// src/controllers/masters/department.controller.js
const Department = require('../../models/masters/department.model');

// Get all departments
const getAllDepartments = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const departments = await Department.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: departments
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching departments',
      error: error.message
    });
  }
};

// Get department by ID
const getDepartmentById = async (req, res) => {
  try {
    const { id } = req.params;
    const department = await Department.findById(id);
    
    if (!department) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }
    
    return res.json({
      success: true,
      data: department
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching department',
      error: error.message
    });
  }
};

// Create a new department
const createDepartment = async (req, res) => {
  try {
    const { name, code, is_active } = req.body;
    
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Department name and code are required'
      });
    }
    
    // Check if department name already exists
    const existingDepartmentWithName = await Department.findByName(name);
    if (existingDepartmentWithName) {
      return res.status(409).json({
        success: false,
        message: 'Department name already exists'
      });
    }
    
    // Check if department code already exists
    const existingDepartmentWithCode = await Department.findByCode(code);
    if (existingDepartmentWithCode) {
      return res.status(409).json({
        success: false,
        message: 'Department code already exists'
      });
    }
    
    // Create the department with the authenticated user's ID
    const department = await Department.create({
      name,
      code,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Department created successfully',
      data: department
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating department',
      error: error.message
    });
  }
};

// Update a department
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, is_active } = req.body;
    
    // Check if department exists
    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== department.name) {
      const existingDepartment = await Department.findByName(name);
      if (existingDepartment) {
        return res.status(409).json({
          success: false,
          message: 'Department name already exists'
        });
      }
    }
    
    // Check if code is being changed and if it already exists
    if (code && code !== department.code) {
      const existingDepartment = await Department.findByCode(code);
      if (existingDepartment) {
        return res.status(409).json({
          success: false,
          message: 'Department code already exists'
        });
      }
    }
    
    // Update the department with the authenticated user's ID as the updater
    const updatedDepartment = await Department.update(id, {
      name,
      code,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Department updated successfully',
      data: updatedDepartment
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating department',
      error: error.message
    });
  }
};

// Delete a department
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if department exists
    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }
    
    // Delete the department
    await Department.delete(id);
    
    return res.json({
      success: true,
      message: 'Department deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting department',
      error: error.message
    });
  }
};

// Toggle department active status
const toggleDepartmentStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if department exists
    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: 'Department not found'
      });
    }
    
    // Toggle the status
    const updatedDepartment = await Department.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Department ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedDepartment
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling department status',
      error: error.message
    });
  }
};

// Bulk delete departments
const bulkDeleteDepartments = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of department IDs is required'
      });
    }
    
    // Perform bulk delete
    await Department.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} departments`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllDepartments,
  getDepartmentById,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  toggleDepartmentStatus,
  bulkDeleteDepartments
};
