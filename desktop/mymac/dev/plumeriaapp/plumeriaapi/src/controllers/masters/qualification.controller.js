// src/controllers/masters/qualification.controller.js
const Qualification = require('../../models/masters/qualification.model');

// Get all qualifications
const getAllQualifications = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const qualifications = await Qualification.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: qualifications
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching qualifications',
      error: error.message
    });
  }
};

// Get qualification by ID
const getQualificationById = async (req, res) => {
  try {
    const { id } = req.params;
    const qualification = await Qualification.findById(id);
    
    if (!qualification) {
      return res.status(404).json({
        success: false,
        message: 'Qualification not found'
      });
    }
    
    return res.json({
      success: true,
      data: qualification
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching qualification',
      error: error.message
    });
  }
};

// Create a new qualification
const createQualification = async (req, res) => {
  try {
    const { name, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Qualification name is required'
      });
    }
    
    // Check if qualification name already exists
    const existingQualificationWithName = await Qualification.findByName(name);
    if (existingQualificationWithName) {
      return res.status(409).json({
        success: false,
        message: 'Qualification name already exists'
      });
    }
    
    // Create the qualification with the authenticated user's ID
    const qualification = await Qualification.create({
      name,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Qualification created successfully',
      data: qualification
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating qualification',
      error: error.message
    });
  }
};

// Update a qualification
const updateQualification = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, is_active } = req.body;
    
    // Check if qualification exists
    const qualification = await Qualification.findById(id);
    if (!qualification) {
      return res.status(404).json({
        success: false,
        message: 'Qualification not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== qualification.name) {
      const existingQualification = await Qualification.findByName(name);
      if (existingQualification) {
        return res.status(409).json({
          success: false,
          message: 'Qualification name already exists'
        });
      }
    }
    
    // Update the qualification with the authenticated user's ID as the updater
    const updatedQualification = await Qualification.update(id, {
      name,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Qualification updated successfully',
      data: updatedQualification
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating qualification',
      error: error.message
    });
  }
};

// Delete a qualification
const deleteQualification = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if qualification exists
    const qualification = await Qualification.findById(id);
    if (!qualification) {
      return res.status(404).json({
        success: false,
        message: 'Qualification not found'
      });
    }
    
    // Delete the qualification
    await Qualification.delete(id);
    
    return res.json({
      success: true,
      message: 'Qualification deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting qualification',
      error: error.message
    });
  }
};

// Toggle qualification active status
const toggleQualificationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if qualification exists
    const qualification = await Qualification.findById(id);
    if (!qualification) {
      return res.status(404).json({
        success: false,
        message: 'Qualification not found'
      });
    }
    
    // Toggle the status
    const updatedQualification = await Qualification.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Qualification ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedQualification
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error toggling qualification status',
      error: error.message
    });
  }
};

// Bulk delete qualifications
const bulkDeleteQualifications = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid array of qualification IDs is required'
      });
    }
    
    // Perform bulk delete
    await Qualification.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} qualifications`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllQualifications,
  getQualificationById,
  createQualification,
  updateQualification,
  deleteQualification,
  toggleQualificationStatus,
  bulkDeleteQualifications
};