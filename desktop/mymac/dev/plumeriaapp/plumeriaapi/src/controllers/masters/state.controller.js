// src/controllers/masters/state.controller.js
const State = require('../../models/masters/state.model');
const Country = require('../../models/masters/country.model');

// Get all states
const getAllStates = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const countryId = req.query.countryId;
    
    let states;
    if (countryId) {
      // Get states by country
      states = await State.findByCountry(countryId, includeInactive);
    } else {
      // Get all states
      states = await State.findAll(includeInactive);
    }
    
    return res.json({
      success: true,
      data: states
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching states',
      error: error.message
    });
  }
};

// Get state by ID
const getStateById = async (req, res) => {
  try {
    const { id } = req.params;
    const state = await State.findById(id);
    
    if (!state) {
      return res.status(404).json({
        success: false,
        message: 'State not found'
      });
    }
    
    return res.json({
      success: true,
      data: state
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching state',
      error: error.message
    });
  }
};

// Create a new state
const createState = async (req, res) => {
  try {
    const { name, code, country_id, is_active } = req.body;
    
    if (!name || !code || !country_id) {
      return res.status(400).json({
        success: false,
        message: 'State name, code, and country_id are required'
      });
    }
    
    // Check if country exists
    const country = await Country.findById(country_id);
    if (!country) {
      return res.status(404).json({
        success: false,
        message: 'Country not found'
      });
    }
    
    // Check if state name already exists for this country
    const existingStateWithName = await State.findByName(name, country_id);
    if (existingStateWithName) {
      return res.status(409).json({
        success: false,
        message: 'State name already exists for this country'
      });
    }
    
    // Check if state code already exists for this country
    const existingStateWithCode = await State.findByCode(code, country_id);
    if (existingStateWithCode) {
      return res.status(409).json({
        success: false,
        message: 'State code already exists for this country'
      });
    }
    
    // Create the state with the authenticated user's ID
    const state = await State.create({
      name,
      code,
      country_id,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'State created successfully',
      data: state
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating state',
      error: error.message
    });
  }
};

// Update an existing state
const updateState = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, country_id, is_active } = req.body;
    
    // Check if state exists
    const state = await State.findById(id);
    if (!state) {
      return res.status(404).json({
        success: false,
        message: 'State not found'
      });
    }
    
    // If country_id is changing, check if the new country exists
    if (country_id && country_id !== state.country_id) {
      const country = await Country.findById(country_id);
      if (!country) {
        return res.status(404).json({
          success: false,
          message: 'Country not found'
        });
      }
    }
    
    // Check if name is being changed and if it already exists for this country
    const effectiveCountryId = country_id || state.country_id;
    if (name && name !== state.name) {
      const existingState = await State.findByName(name, effectiveCountryId);
      if (existingState) {
        return res.status(409).json({
          success: false,
          message: 'State name already exists for this country'
        });
      }
    }
    
    // Check if code is being changed and if it already exists for this country
    if (code && code !== state.code) {
      const existingState = await State.findByCode(code, effectiveCountryId);
      if (existingState) {
        return res.status(409).json({
          success: false,
          message: 'State code already exists for this country'
        });
      }
    }
    
    // Update the state with the authenticated user's ID as the updater
    const updatedState = await State.update(id, {
      name,
      code,
      country_id,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'State updated successfully',
      data: updatedState
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating state',
      error: error.message
    });
  }
};

// Delete a state
const deleteState = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if state exists
    const state = await State.findById(id);
    if (!state) {
      return res.status(404).json({
        success: false,
        message: 'State not found'
      });
    }
    
    // Delete the state
    await State.delete(id);
    
    return res.json({
      success: true,
      message: 'State deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting state',
      error: error.message
    });
  }
};

// Toggle state active status
const toggleStateStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if state exists
    const state = await State.findById(id);
    if (!state) {
      return res.status(404).json({
        success: false,
        message: 'State not found'
      });
    }
    
    // Toggle the status
    const updatedState = await State.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `State ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedState
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: `Error ${req.body.is_active ? 'activating' : 'deactivating'} state`,
      error: error.message
    });
  }
};

// Bulk delete states
const bulkDeleteStates = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'State IDs array is required'
      });
    }
    
    // Check if all states exist
    const validationPromises = ids.map(id => State.findById(id));
    const states = await Promise.all(validationPromises);
    
    const notFoundIds = ids.filter((id, index) => !states[index]);
    
    if (notFoundIds.length > 0) {
      return res.status(404).json({
        success: false,
        message: `States not found with IDs: ${notFoundIds.join(', ')}`
      });
    }
    
    // Perform bulk delete
    await State.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} states`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllStates,
  getStateById,
  createState,
  updateState,
  deleteState,
  toggleStateStatus,
  bulkDeleteStates
};