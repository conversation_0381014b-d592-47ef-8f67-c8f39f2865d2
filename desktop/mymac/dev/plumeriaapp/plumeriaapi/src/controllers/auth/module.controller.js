const ModuleService = require('../../services/module.service');

// Get all modules
const getAllModules = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const modules = await ModuleService.getAllModules(includeInactive);
    return res.json({
      success: true,
      data: modules
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching modules',
      error: error.message
    });
  }
};

// Get module by ID
const getModuleById = async (req, res) => {
  try {
    const { id } = req.params;
    const includeInactive = req.query.includeInactive === 'true';
    const module = await ModuleService.getModuleById(id, includeInactive);

    return res.json({
      success: true,
      data: module
    });
  } catch (error) {
    if (error.message === 'Module not found' || error.message === 'Module is inactive') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error fetching module',
      error: error.message
    });
  }
};

// Create a new module
const createModule = async (req, res) => {
  try {
    const { name, description, is_active } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Module name is required'
      });
    }

    // Get the user ID from the authenticated request
    const userId = req.user.id;

    const module = await ModuleService.createModule({
      name,
      description,
      is_active,
      created_by: userId
    });

    return res.status(201).json({
      success: true,
      message: 'Module created with standard permissions',
      data: module
    });
  } catch (error) {
    if (error.message === 'Module name already exists') {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error creating module',
      error: error.message
    });
  }
};

// Update an existing module
const updateModule = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, is_active } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Module name is required'
      });
    }

    const module = await ModuleService.updateModule(id, { name, description, is_active });

    return res.json({
      success: true,
      message: 'Module updated successfully',
      data: module
    });
  } catch (error) {
    if (error.message === 'Module not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    if (error.message === 'Module name already exists') {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error updating module',
      error: error.message
    });
  }
};

// Delete a module
const deleteModule = async (req, res) => {
  try {
    const { id } = req.params;
    await ModuleService.deleteModule(id);

    return res.json({
      success: true,
      message: 'Module deleted successfully'
    });
  } catch (error) {
    if (error.message === 'Module not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error deleting module',
      error: error.message
    });
  }
};

// Toggle module active status
const toggleModuleStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }

    const module = await ModuleService.toggleModuleStatus(id, Boolean(is_active));

    return res.json({
      success: true,
      message: `Module ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: module
    });
  } catch (error) {
    if (error.message === 'Module not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: `Error ${req.body.is_active ? 'activating' : 'deactivating'} module`,
      error: error.message
    });
  }
};

// Add permission to module
const addPermissionToModule = async (req, res) => {
  try {
    const { moduleId, permissionId } = req.params;

    await ModuleService.addPermissionToModule(moduleId, permissionId);

    return res.json({
      success: true,
      message: 'Permission added to module successfully'
    });
  } catch (error) {
    if (error.message === 'Module not found' || error.message === 'Permission not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error adding permission to module',
      error: error.message
    });
  }
};

// Remove permission from module
const removePermissionFromModule = async (req, res) => {
  try {
    const { moduleId, permissionId } = req.params;

    await ModuleService.removePermissionFromModule(moduleId, permissionId);

    return res.json({
      success: true,
      message: 'Permission removed from module successfully'
    });
  } catch (error) {
    if (error.message === 'Module not found' || error.message === 'Permission not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error removing permission from module',
      error: error.message
    });
  }
};

// Grant user access to module
const grantUserAccess = async (req, res) => {
  try {
    const { userId, moduleId } = req.params;

    await ModuleService.grantUserAccess(userId, moduleId);

    return res.json({
      success: true,
      message: 'User granted access to module successfully'
    });
  } catch (error) {
    if (error.message === 'Module not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error granting user access to module',
      error: error.message
    });
  }
};

// Revoke user access to module
const revokeUserAccess = async (req, res) => {
  try {
    const { userId, moduleId } = req.params;

    await ModuleService.revokeUserAccess(userId, moduleId);

    return res.json({
      success: true,
      message: 'User access to module revoked successfully'
    });
  } catch (error) {
    if (error.message === 'Module not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error revoking user access to module',
      error: error.message
    });
  }
};

// Get users with access to module
const getUsersWithAccess = async (req, res) => {
  try {
    const { moduleId } = req.params;

    const users = await ModuleService.getUsersWithAccess(moduleId);

    return res.json({
      success: true,
      data: users
    });
  } catch (error) {
    if (error.message === 'Module not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error fetching users with module access',
      error: error.message
    });
  }
};

// Bulk delete modules
const bulkDeleteModules = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Module IDs array is required'
      });
    }

    const result = await ModuleService.bulkDeleteModules(ids);

    return res.json({
      success: true,
      message: `Successfully deleted ${result.count} modules`,
      data: result
    });
  } catch (error) {
    // Check if the error message contains "Modules not found"
    if (error.message.includes('Modules not found')) {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }

    if (error.message === 'Invalid module IDs for bulk deletion') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllModules,
  getModuleById,
  createModule,
  updateModule,
  deleteModule,
  toggleModuleStatus,
  addPermissionToModule,
  removePermissionFromModule,
  grantUserAccess,
  revokeUserAccess,
  getUsersWithAccess,
  bulkDeleteModules
};