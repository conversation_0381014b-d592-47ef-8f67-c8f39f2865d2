const RoleModulePermission = require('../../models/auth/role_module_permission.model');
const Role = require('../../models/auth/role.model');
const Module = require('../../models/auth/module.model');
const Permission = require('../../models/auth/permission.model');

// Get all role-module-permissions
const getAllRoleModulePermissions = async (req, res) => {
  try {
    const roleModulePermissions = await RoleModulePermission.findAll();
    
    // Group by role and module for better presentation
    const groupedPermissions = {};
    
    roleModulePermissions.forEach(rmp => {
      if (!groupedPermissions[rmp.role_name]) {
        groupedPermissions[rmp.role_name] = {};
      }
      
      if (!groupedPermissions[rmp.role_name][rmp.module_name]) {
        groupedPermissions[rmp.role_name][rmp.module_name] = [];
      }
      
      groupedPermissions[rmp.role_name][rmp.module_name].push(rmp.permission_name);
    });
    
    return res.json({
      success: true,
      data: groupedPermissions
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching role-module-permissions',
      error: error.message
    });
  }
};

// Get all permissions for a role
const getRolePermissions = async (req, res) => {
  try {
    const { roleId } = req.params;
    
    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }
    
    const permissions = await RoleModulePermission.findByRole(roleId);
    
    // Group by module for better presentation
    const modulePermissions = {};
    
    permissions.forEach(p => {
      if (!modulePermissions[p.module_name]) {
        modulePermissions[p.module_name] = [];
      }
      
      modulePermissions[p.module_name].push(p.permission_name);
    });
    
    return res.json({
      success: true,
      data: {
        role: role.name,
        modulePermissions
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching role permissions',
      error: error.message
    });
  }
};

// Get permissions for a specific role-module combination
const getRoleModulePermissions = async (req, res) => {
  try {
    const { roleId, moduleId } = req.params;
    
    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }
    
    // Check if module exists
    const module = await Module.findById(moduleId);
    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }
    
    const permissions = await RoleModulePermission.findByRoleAndModule(roleId, moduleId);
    
    return res.json({
      success: true,
      data: {
        role: role.name,
        module: module.name,
        permissions: permissions.map(p => ({
          id: p.permission_id,
          name: p.permission_name,
          description: p.permission_description
        }))
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching role-module permissions',
      error: error.message
    });
  }
};

// Assign permission to role for a module
const assignRoleModulePermission = async (req, res) => {
  try {
    const { roleId, moduleId, permissionId } = req.params;
    
    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }
    
    // Check if module exists
    const module = await Module.findById(moduleId);
    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }
    
    // Check if permission exists
    const permission = await Permission.findById(permissionId);
    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }
    
    // Check if the permission is already assigned
    const exists = await RoleModulePermission.exists(roleId, moduleId, permissionId);
    
    if (exists) {
      return res.status(409).json({
        success: false,
        message: 'Permission already assigned to this role for this module'
      });
    }
    
    // Assign the permission
    await RoleModulePermission.create(roleId, moduleId, permissionId);
    
    return res.status(201).json({
      success: true,
      message: 'Permission assigned successfully',
      data: {
        role: role.name,
        module: module.name,
        permission: permission.name
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error assigning permission',
      error: error.message
    });
  }
};

// Remove permission from role for a module
const removeRoleModulePermission = async (req, res) => {
  try {
    const { roleId, moduleId, permissionId } = req.params;
    
    // Check if the entry exists
    const exists = await RoleModulePermission.exists(roleId, moduleId, permissionId);
    
    if (!exists) {
      return res.status(404).json({
        success: false,
        message: 'Permission not assigned to this role for this module'
      });
    }
    
    // Remove the permission
    await RoleModulePermission.delete(roleId, moduleId, permissionId);
    
    return res.json({
      success: true,
      message: 'Permission removed successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error removing permission',
      error: error.message
    });
  }
};

// Assign all permissions of a module to a role
const assignAllModulePermissionsToRole = async (req, res) => {
  try {
    const { roleId, moduleId } = req.params;
    
    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }
    
    // Check if module exists
    const module = await Module.findById(moduleId);
    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }
    
    // Get all module permissions
    const modulePermissions = await RoleModulePermission.getModulePermissions(moduleId);
    
    if (modulePermissions.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No permissions found for this module'
      });
    }
    
    // First, remove existing permissions
    await RoleModulePermission.deleteByRoleAndModule(roleId, moduleId);
    
    // Then add all permissions
    const insertPromises = modulePermissions.map(p => 
      RoleModulePermission.create(roleId, moduleId, p.id)
    );
    
    await Promise.all(insertPromises);
    
    return res.json({
      success: true,
      message: `All permissions for ${module.name} module assigned to ${role.name} role`,
      data: {
        permissionsCount: modulePermissions.length
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error assigning permissions',
      error: error.message
    });
  }
};

// Remove all permissions of a module from a role
const removeAllModulePermissionsFromRole = async (req, res) => {
  try {
    const { roleId, moduleId } = req.params;
    
    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }
    
    // Check if module exists
    const module = await Module.findById(moduleId);
    if (!module) {
      return res.status(404).json({
        success: false,
        message: 'Module not found'
      });
    }
    
    // Remove all permissions
    const result = await RoleModulePermission.deleteByRoleAndModule(roleId, moduleId);
    
    return res.json({
      success: true,
      message: `All permissions for ${module.name} module removed from ${role.name} role`,
      data: {
        removedCount: result.affectedRows
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error removing permissions',
      error: error.message
    });
  }
};

module.exports = {
  getAllRoleModulePermissions,
  getRolePermissions,
  getRoleModulePermissions,
  assignRoleModulePermission,
  removeRoleModulePermission,
  assignAllModulePermissionsToRole,
  removeAllModulePermissionsFromRole
};