const AuthService = require('../../services/auth.service');
const RoleModulePermission = require('../../models/auth/role_module_permission.model');

// Login controller function
const login = async (req, res) => {
  try {
    const { identifier, password } = req.body;
    
    if (!identifier || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username/email and password are required'
      });
    }
    
    const authData = await AuthService.login(identifier, password);
    
    return res.json({
      success: true,
      message: 'Login successful',
      data: authData
    });
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: error.message || 'Authentication failed'
    });
  }
};

// Registration controller function
const register = async (req, res) => {
  try {
    const { username, email, password, name, phone } = req.body;
    
    // Validate required fields
    if (!username || !email || !password || !name) {
      return res.status(400).json({
        success: false,
        message: 'Required fields are missing'
      });
    }
    
    // Register the user
    const user = await AuthService.register({
      username,
      email,
      password,
      name,
      phone
    });
    
    return res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name
      }
    });
  } catch (error) {
    if (error.message.includes('already in use')) {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error registering user',
      error: error.message
    });
  }
};

// Token refresh controller function
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token is required'
      });
    }
    
    const tokens = await AuthService.refreshToken(refreshToken);
    
    return res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: tokens
    });
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: error.message || 'Invalid refresh token'
    });
  }
};

// Permission check controller function
const checkPermission = async (req, res) => {
  try {
    const { module, permission } = req.body;
    const userId = req.user.id;
    
    const hasPermission = await RoleModulePermission.checkUserPermission(
      userId, 
      module, 
      permission
    );
    
    return res.json({ hasPermission });
  } catch (error) {
    console.error('Permission check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking permission',
      error: error.message
    });
  }
};

// Export the controller functions
module.exports = {
  login,
  register,
  refreshToken,
  checkPermission
};