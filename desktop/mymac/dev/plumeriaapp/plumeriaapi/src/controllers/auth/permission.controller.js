const Permission = require('../../models/auth/permission.model');

// Get all permissions
const getAllPermissions = async (req, res) => {
  try {
    const permissions = await Permission.findAll();
    
    return res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching permissions',
      error: error.message
    });
  }
};

// Get permission by ID
const getPermissionById = async (req, res) => {
  try {
    const { id } = req.params;
    const permission = await Permission.findById(id);
    
    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }
    
    return res.json({
      success: true,
      data: permission
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching permission',
      error: error.message
    });
  }
};

// Create a new permission
const createPermission = async (req, res) => {
  try {
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Permission name is required'
      });
    }
    
    // Check if permission with this name already exists
    const existingPermission = await Permission.findByName(name);
    if (existingPermission) {
      return res.status(409).json({
        success: false,
        message: 'Permission with this name already exists'
      });
    }
    
    const permission = await Permission.create({ name, description });
    
    return res.status(201).json({
      success: true,
      message: 'Permission created successfully',
      data: permission
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating permission',
      error: error.message
    });
  }
};

// Update an existing permission
const updatePermission = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    
    // Check if permission exists
    const permission = await Permission.findById(id);
    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }
    
    // Check if new name already exists (if name is being changed)
    if (name && name !== permission.name) {
      const existingPermission = await Permission.findByName(name);
      if (existingPermission) {
        return res.status(409).json({
          success: false,
          message: 'Permission with this name already exists'
        });
      }
    }
    
    // Update the permission
    const updatedPermission = await Permission.update(id, { 
      name: name || permission.name, 
      description: description !== undefined ? description : permission.description 
    });
    
    return res.json({
      success: true,
      message: 'Permission updated successfully',
      data: updatedPermission
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating permission',
      error: error.message
    });
  }
};

// Delete a permission
const deletePermission = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if permission exists
    const permission = await Permission.findById(id);
    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }
    
    // Check if permission is in use
    const modules = await Permission.getModulesWithPermission(id);
    const roles = await Permission.getRolesWithPermission(id);
    
    if (modules.length > 0 || roles.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Cannot delete permission that is in use',
        data: {
          modules: modules.map(m => m.name),
          roles: roles.map(r => r.name)
        }
      });
    }
    
    // Delete the permission
    await Permission.delete(id);
    
    return res.json({
      success: true,
      message: 'Permission deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting permission',
      error: error.message
    });
  }
};

module.exports = {
  getAllPermissions,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission
};
