// src/routes/masters/suppliertype.routes.js
const express = require('express');
const supplierTypeController = require('../../controllers/masters/suppliertype.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing supplier types
router.get('/', hasModulePermission('masters', 'read'), supplierTypeController.getAllSupplierTypes);
router.get('/:id', hasModulePermission('masters', 'read'), supplierTypeController.getSupplierTypeById);
router.post('/', hasModulePermission('masters', 'create'), supplierTypeController.createSupplierType);
router.put('/:id', hasModulePermission('masters', 'update'), supplierTypeController.updateSupplierType);
router.delete('/:id', hasModulePermission('masters', 'delete'), supplierTypeController.deleteSupplierType);
router.patch('/:id/status', hasModulePermission('masters', 'update'), supplierTypeController.toggleSupplierTypeStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), supplierTypeController.bulkDeleteSupplierTypes);

module.exports = router;