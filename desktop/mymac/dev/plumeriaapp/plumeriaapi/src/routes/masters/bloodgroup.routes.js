// src/routes/masters/bloodgroup.routes.js
const express = require('express');
const bloodGroupController = require('../../controllers/masters/bloodgroup.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing blood groups
router.get('/', hasModulePermission('masters', 'read'), bloodGroupController.getAllBloodGroups);
router.get('/:id', hasModulePermission('masters', 'read'), bloodGroupController.getBloodGroupById);
router.post('/', hasModulePermission('masters', 'create'), bloodGroupController.createBloodGroup);
router.put('/:id', hasModulePermission('masters', 'update'), bloodGroupController.updateBloodGroup);
router.delete('/:id', hasModulePermission('masters', 'delete'), bloodGroupController.deleteBloodGroup);
router.patch('/:id/status', hasModulePermission('masters', 'update'), bloodGroupController.toggleBloodGroupStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), bloodGroupController.bulkDeleteBloodGroups);

module.exports = router;
