// src/routes/masters/city.routes.js
const express = require('express');
const cityController = require('../../controllers/masters/city.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing cities
router.get('/', hasModulePermission('masters', 'read'), cityController.getAllCities);
router.get('/:id', hasModulePermission('masters', 'read'), cityController.getCityById);
router.post('/', hasModulePermission('masters', 'create'), cityController.createCity);
router.put('/:id', hasModulePermission('masters', 'update'), cityController.updateCity);
router.delete('/:id', hasModulePermission('masters', 'delete'), cityController.deleteCity);
router.patch('/:id/status', hasModulePermission('masters', 'update'), cityController.toggleCityStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), cityController.bulkDeleteCities);

module.exports = router;
