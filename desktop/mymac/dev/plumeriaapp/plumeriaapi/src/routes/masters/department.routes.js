// src/routes/masters/department.routes.js
const express = require('express');
const departmentController = require('../../controllers/masters/department.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing departments
router.get('/', hasModulePermission('masters', 'read'), departmentController.getAllDepartments);
router.get('/:id', hasModulePermission('masters', 'read'), departmentController.getDepartmentById);
router.post('/', hasModulePermission('masters', 'create'), departmentController.createDepartment);
router.put('/:id', hasModulePermission('masters', 'update'), departmentController.updateDepartment);
router.delete('/:id', hasModulePermission('masters', 'delete'), departmentController.deleteDepartment);
router.patch('/:id/status', hasModulePermission('masters', 'update'), departmentController.toggleDepartmentStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), departmentController.bulkDeleteDepartments);

module.exports = router;
