// src/routes/masters/measurementunit.routes.js
const express = require('express');
const measurementUnitController = require('../../controllers/masters/measurementunit.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing measurement units
router.get('/', hasModulePermission('masters', 'read'), measurementUnitController.getAllMeasurementUnits);
router.get('/:id', hasModulePermission('masters', 'read'), measurementUnitController.getMeasurementUnitById);
router.post('/', hasModulePermission('masters', 'create'), measurementUnitController.createMeasurementUnit);
router.put('/:id', hasModulePermission('masters', 'update'), measurementUnitController.updateMeasurementUnit);
router.delete('/:id', hasModulePermission('masters', 'delete'), measurementUnitController.deleteMeasurementUnit);
router.patch('/:id/status', hasModulePermission('masters', 'update'), measurementUnitController.toggleMeasurementUnitStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), measurementUnitController.bulkDeleteMeasurementUnits);

module.exports = router;
