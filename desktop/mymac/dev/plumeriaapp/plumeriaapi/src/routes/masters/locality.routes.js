// src/routes/masters/locality.routes.js
const express = require('express');
const localityController = require('../../controllers/masters/locality.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing localities
router.get('/', hasModulePermission('masters', 'read'), localityController.getAllLocalities);
router.get('/:id', hasModulePermission('masters', 'read'), localityController.getLocalityById);
router.post('/', hasModulePermission('masters', 'create'), localityController.createLocality);
router.put('/:id', hasModulePermission('masters', 'update'), localityController.updateLocality);
router.delete('/:id', hasModulePermission('masters', 'delete'), localityController.deleteLocality);
router.patch('/:id/status', hasModulePermission('masters', 'update'), localityController.toggleLocalityStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), localityController.bulkDeleteLocalities);

module.exports = router;
