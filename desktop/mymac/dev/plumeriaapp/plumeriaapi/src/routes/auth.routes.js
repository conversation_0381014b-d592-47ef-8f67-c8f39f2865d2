const express = require('express');
const AuthController = require('../controllers/auth/auth.controller');
const { verifyToken } = require('../middleware/auth.middleware'); 

const router = express.Router();

// Route for user login
router.post('/login', AuthController.login);

// Route for user registration
router.post('/register', AuthController.register);

// Route for refreshing tokens
router.post('/refresh-token', AuthController.refreshToken);
router.post('/check-permission', verifyToken, AuthController.checkPermission);

module.exports = router;