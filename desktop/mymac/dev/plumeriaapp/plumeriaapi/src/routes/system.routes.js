const express = require('express');
const { verifyToken } = require('../middleware/auth.middleware');
const db = require('../config/database');
const Role = require('../models/auth/role.model');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Special route to make a user an admin - this bypasses normal permission checks
router.post('/make-admin', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }
    
    // Check if admin role exists
    console.log('Checking if admin role exists');
    const adminRoleQuery = 'SELECT * FROM roles WHERE name = ?';
    const adminRoles = await db.query(adminRoleQuery, ['admin']);
    
    let adminRoleId;
    
    if (adminRoles.length === 0) {
      console.log('Admin role does not exist, creating it');
      // Create admin role
      const createRoleQuery = 'INSERT INTO roles (name, description, is_active) VALUES (?, ?, ?)';
      const roleResult = await db.query(createRoleQuery, ['admin', 'Administrator role with all permissions', 1]);
      adminRoleId = roleResult.insertId;
      console.log(`Created admin role with ID: ${adminRoleId}`);
    } else {
      adminRoleId = adminRoles[0].id;
      console.log(`Found existing admin role with ID: ${adminRoleId}`);
    }
    
    // Check if user already has admin role
    console.log(`Checking if user ${userId} already has admin role`);
    const userRoleQuery = 'SELECT * FROM user_roles WHERE user_id = ? AND role_id = ?';
    const userRoles = await db.query(userRoleQuery, [userId, adminRoleId]);
    
    if (userRoles.length > 0) {
      console.log(`User ${userId} already has admin role`);
      return res.json({
        success: true,
        message: 'User already has admin role'
      });
    }
    
    // Assign admin role to user
    console.log(`Assigning admin role to user ${userId}`);
    const assignRoleQuery = 'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)';
    await db.query(assignRoleQuery, [userId, adminRoleId]);
    
    // Make sure we have basic modules
    console.log('Checking for basic modules');
    const modules = [
      { name: 'user', description: 'User Management' },
      { name: 'role', description: 'Role Management' },
      { name: 'module', description: 'Module Management' },
      { name: 'permission', description: 'Permission Management' }
    ];
    
    // Make sure permissions exist
    console.log('Checking for basic permissions');
    const permissions = [
      { id: 1, name: 'create', description: 'Create permission' },
      { id: 2, name: 'read', description: 'Read permission' },
      { id: 3, name: 'update', description: 'Update permission' },
      { id: 4, name: 'delete', description: 'Delete permission' }
    ];
    
    // Check and create permissions if needed
    for (const perm of permissions) {
      const permQuery = 'SELECT * FROM permissions WHERE id = ?';
      const existingPerms = await db.query(permQuery, [perm.id]);
      
      if (existingPerms.length === 0) {
        console.log(`Creating permission: ${perm.name}`);
        const createPermQuery = 'INSERT INTO permissions (id, name, description) VALUES (?, ?, ?)';
        await db.query(createPermQuery, [perm.id, perm.name, perm.description]);
      }
    }
    
    // Check and create modules if needed
    for (const module of modules) {
      const moduleQuery = 'SELECT * FROM modules WHERE name = ?';
      const existingModules = await db.query(moduleQuery, [module.name]);
      
      if (existingModules.length === 0) {
        console.log(`Creating module: ${module.name}`);
        const createModuleQuery = 'INSERT INTO modules (name, description, is_active) VALUES (?, ?, ?)';
        await db.query(createModuleQuery, [module.name, module.description, 1]);
      }
    }
    
    // Now assign all permissions to the admin role for all modules
    console.log('Assigning permissions to admin role');
    
    // Get all modules
    const allModulesQuery = 'SELECT * FROM modules';
    const allModules = await db.query(allModulesQuery);
    
    // For each module, assign all permissions to the admin role
    for (const module of allModules) {
      for (const perm of permissions) {
        // Check if the permission is already assigned
        const permCheckQuery = `
          SELECT * FROM role_module_permissions 
          WHERE role_id = ? AND module_id = ? AND permission_id = ?
        `;
        const existingPerms = await db.query(permCheckQuery, [adminRoleId, module.id, perm.id]);
        
        if (existingPerms.length === 0) {
          console.log(`Assigning ${perm.name} permission on ${module.name} module to admin role`);
          const assignPermQuery = `
            INSERT INTO role_module_permissions (role_id, module_id, permission_id)
            VALUES (?, ?, ?)
          `;
          await db.query(assignPermQuery, [adminRoleId, module.id, perm.id]);
        }
      }
    }
    
    return res.json({
      success: true,
      message: 'User has been made an admin with all permissions'
    });
    
  } catch (error) {
    console.error('Error making user admin:', error);
    return res.status(500).json({
      success: false,
      message: 'Error making user admin',
      error: error.message
    });
  }
});

module.exports = router;
