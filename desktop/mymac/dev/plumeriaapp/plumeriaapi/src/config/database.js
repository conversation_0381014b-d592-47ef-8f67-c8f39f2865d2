const mysql = require('mysql2');
require('dotenv').config();

// Create a connection pool with higher limits for many concurrent users
const pool = mysql.createPool({
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 25,  // Suitable for high traffic
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 30000
});

// Get a Promise wrapped instance of the pool
const promisePool = pool.promise();

// Handle unexpected errors to prevent app crash
pool.on('error', (err) => {
  console.error('Unexpected error on database connection:', err);
  // Log but don't crash the application
});

module.exports = {
  pool: promisePool,
  
  // Helper function to execute queries
  query: async (sql, params) => {
    try {
      const [rows] = await promisePool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  },
  
  // Close all connections (useful for graceful shutdown)
  close: async () => {
    try {
      await promisePool.end();
      console.log('Database pool connections closed');
    } catch (error) {
      console.error('Error closing database pool:', error);
      throw error;
    }
  }
};