const multer = require('multer');
const path = require('path');

/**
 * Upload Middleware
 * Handles file uploads with validation
 */

// Configure multer for memory storage (we'll handle file saving manually)
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (req, file, cb) => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
    files: 1 // Only one file at a time
  },
  fileFilter: fileFilter
});

/**
 * Middleware for single profile picture upload
 */
const uploadProfilePicture = upload.single('profile_picture');

/**
 * Error handling middleware for multer
 * @param {Error} error - Multer error
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File size too large. Maximum size is 2MB'
      });
    }

    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Only one file is allowed'
      });
    }

    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Unexpected field name. Use "profile_picture" as field name'
      });
    }
  }

  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }

  // Generic upload error
  return res.status(400).json({
    success: false,
    message: 'File upload failed',
    error: error.message
  });
};

/**
 * Combined middleware for profile picture upload with error handling
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
const uploadProfilePictureWithErrorHandling = (req, res, next) => {
  console.log('=== UPLOAD MIDDLEWARE ===');
  console.log('Content-Type:', req.headers['content-type']);
  console.log('Request method:', req.method);
  console.log('Request URL:', req.url);
  console.log('Authorization header present:', !!req.headers.authorization);
  console.log('Body keys:', Object.keys(req.body || {}));

  uploadProfilePicture(req, res, (error) => {
    if (error) {
      console.log('Upload middleware error:', error.message);
      console.log('Error code:', error.code);
      return handleUploadError(error, req, res, next);
    }
    console.log('Upload middleware success, file:', req.file ? {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size
    } : 'missing');
    next();
  });
};

module.exports = {
  uploadProfilePicture: uploadProfilePictureWithErrorHandling,
  handleUploadError
};
