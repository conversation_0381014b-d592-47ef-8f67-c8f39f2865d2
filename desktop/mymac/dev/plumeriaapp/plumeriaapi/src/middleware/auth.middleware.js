const jwt = require('jsonwebtoken');
const jwtConfig = require('../config/jwt');
const db = require('../config/database');

// Verify the JWT token
const verifyToken = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No authentication token provided'
    });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    // Verify the token
    const decoded = jwt.verify(token, jwtConfig.secret);
    
    // Add the user info to the request object
    req.user = decoded;
    
    // Continue to the next middleware or route handler
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
};

// Check if user has the required module permission - Database driven
const hasModulePermission = (moduleName, action) => {
  return async (req, res, next) => {
    try {
      console.log(`---- Permission check for ${moduleName} - ${action} ----`);
      
      // verifyToken should be called before this middleware
      if (!req.user) {
        console.log('No user in request');
        return res.status(401).json({
          success: false,
          message: 'Unauthorized access'
        });
      }
      
      const userId = req.user.id;
      console.log(`User ID: ${userId}`);
      
      // Get user's active roles from database
      const userRoles = await getUserRolesFromDB(userId);
      console.log(`User roles:`, userRoles.map(r => ({ id: r.id, name: r.name, active: r.is_active })));
      
      if (userRoles.length === 0) {
        console.log('User has no roles');
        return res.status(403).json({
          success: false,
          message: 'Access forbidden: User has no roles assigned'
        });
      }
      
      // Only use active roles for permission checking
      const activeRoleIds = userRoles
        .filter(role => role.is_active === 1)
        .map(role => role.id);
      
      console.log(`Active role IDs: ${activeRoleIds.join(', ')}`);
      
      if (activeRoleIds.length === 0) {
        console.log('User has no active roles');
        return res.status(403).json({
          success: false,
          message: 'Access forbidden: User has no active roles'
        });
      }
      
      // Get module ID for the module name
      const module = await getModuleByName(moduleName);
      console.log(`Module lookup for "${moduleName}":`, module);
      
      if (!module) {
        console.log(`Module "${moduleName}" not found`);
        return res.status(403).json({
          success: false,
          message: `Access forbidden: Module ${moduleName} not found`
        });
      }
      
      // Get permission ID for the action
      const permissionId = getPermissionIdForAction(action);
      console.log(`Permission ID for "${action}": ${permissionId}`);
      
      if (!permissionId) {
        console.log(`Invalid action: ${action}`);
        return res.status(403).json({
          success: false,
          message: `Access forbidden: Invalid action ${action}`
        });
      }
      
      // Check if any of the user's active roles have this permission
      const hasPermission = await checkRoleModulePermission(activeRoleIds, module.id, permissionId);
      console.log(`Has permission: ${hasPermission}`);
      
      if (!hasPermission) {
        console.log(`Permission denied: Cannot ${action} in ${moduleName} module`);
        return res.status(403).json({
          success: false,
          message: `Access forbidden: Cannot ${action} in ${moduleName} module`
        });
      }
      
      console.log('Permission granted');
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions'
      });
    }
  };
};
// Check if user has any of the specified module permissions - Database driven
const hasAnyModulePermission = (permissionsMap) => {
  return async (req, res, next) => {
    try {
      // verifyToken should be called before this middleware
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized access'
        });
      }
      
      const userId = req.user.id;
      
      // Get user's active roles from database
      const userRoles = await getUserRolesFromDB(userId);
      
      // Only use active roles for permission checking
      const activeRoleIds = userRoles
        .filter(role => role.is_active === 1)
        .map(role => role.id);
      
      if (activeRoleIds.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Access forbidden: User has no active roles'
        });
      }
      
      // Check if user has any of the required permissions
      for (const [moduleName, actions] of Object.entries(permissionsMap)) {
        // Get module ID for the module name
        const module = await getModuleByName(moduleName);
        if (!module) continue;
        
        // Check permissions for each action
        for (const action of actions) {
          const permissionId = getPermissionIdForAction(action);
          if (!permissionId) continue;
          
          // Check if any of the user's active roles have this permission
          const hasPermission = await checkRoleModulePermission(activeRoleIds, module.id, permissionId);
          
          if (hasPermission) {
            return next(); // User has at least one of the required permissions
          }
        }
      }
      
      // If we get here, the user doesn't have any of the required permissions
      return res.status(403).json({
        success: false,
        message: 'Access forbidden: Insufficient permissions'
      });
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions'
      });
    }
  };
};

// Check if user has all of the specified module permissions - Database driven
const hasAllModulePermissions = (permissionsMap) => {
  return async (req, res, next) => {
    try {
      // verifyToken should be called before this middleware
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized access'
        });
      }
      
      const userId = req.user.id;
      
      // Get user's active roles from database
      const userRoles = await getUserRolesFromDB(userId);
      
      // Only use active roles for permission checking
      const activeRoleIds = userRoles
        .filter(role => role.is_active === 1)
        .map(role => role.id);
      
      if (activeRoleIds.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Access forbidden: User has no active roles'
        });
      }
      
      // Check if user has all of the required permissions
      for (const [moduleName, actions] of Object.entries(permissionsMap)) {
        // Get module ID for the module name
        const module = await getModuleByName(moduleName);
        if (!module) {
          return res.status(403).json({
            success: false,
            message: `Access forbidden: Module ${moduleName} not found`
          });
        }
        
        // Check permissions for each action
        for (const action of actions) {
          const permissionId = getPermissionIdForAction(action);
          if (!permissionId) {
            return res.status(403).json({
              success: false,
              message: `Access forbidden: Invalid action ${action}`
            });
          }
          
          // Check if any of the user's active roles have this permission
          const hasPermission = await checkRoleModulePermission(activeRoleIds, module.id, permissionId);
          
          if (!hasPermission) {
            return res.status(403).json({
              success: false,
              message: `Access forbidden: Cannot ${action} in ${moduleName} module`
            });
          }
        }
      }
      
      // If we get here, the user has all of the required permissions
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions'
      });
    }
  };
};

// Check if user has a specific role - Database driven
const hasRole = (roleName) => {
  return async (req, res, next) => {
    try {
      // verifyToken should be called before this middleware
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized access'
        });
      }
      
      const userId = req.user.id;
      
      // Get user's active roles from database
      const userRoles = await getUserRolesFromDB(userId);
      
      // Check if user has the specified role and it's active
      const hasActiveRole = userRoles.some(role => 
        role.name === roleName && role.is_active === 1
      );
      
      if (!hasActiveRole) {
        return res.status(403).json({
          success: false,
          message: `Access forbidden: ${roleName} role required`
        });
      }
      
      next();
    } catch (error) {
      console.error('Role check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking roles'
      });
    }
  };
};

// Check if user has any of the specified roles - Database driven
const hasAnyRole = (roleNames) => {
  return async (req, res, next) => {
    try {
      // verifyToken should be called before this middleware
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized access'
        });
      }
      
      const userId = req.user.id;
      
      // Get user's active roles from database
      const userRoles = await getUserRolesFromDB(userId);
      
      // Check if user has any of the specified roles and they're active
      const hasAnyActiveRole = userRoles.some(role => 
        roleNames.includes(role.name) && role.is_active === 1
      );
      
      if (!hasAnyActiveRole) {
        return res.status(403).json({
          success: false,
          message: `Access forbidden: One of these roles required: ${roleNames.join(', ')}`
        });
      }
      
      next();
    } catch (error) {
      console.error('Role check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking roles'
      });
    }
  };
};

// Dynamically determine module and action from the request path - Database driven
const dynamicModulePermission = () => {
  return async (req, res, next) => {
    try {
      // verifyToken should be called before this middleware
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized access'
        });
      }
      
      // Extract module name from path (assuming path format like /api/moduleName/...)
      const path = req.path.split('/');
      const moduleName = path[1] || ''; // First segment after leading slash
      
      // Determine action based on HTTP method
      let action;
      switch (req.method) {
        case 'GET':
          action = 'read';
          break;
        case 'POST':
          action = 'create';
          break;
        case 'PUT':
        case 'PATCH':
          action = 'update';
          break;
        case 'DELETE':
          action = 'delete';
          break;
        default:
          action = 'read';
      }
      
      const userId = req.user.id;
      
      // Get user's active roles from database
      const userRoles = await getUserRolesFromDB(userId);
      
      // Only use active roles for permission checking
      const activeRoleIds = userRoles
        .filter(role => role.is_active === 1)
        .map(role => role.id);
      
      if (activeRoleIds.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'Access forbidden: User has no active roles'
        });
      }
      
      // Get module ID for the module name
      const module = await getModuleByName(moduleName);
      if (!module) {
        return res.status(403).json({
          success: false,
          message: `Access forbidden: Module ${moduleName} not found`
        });
      }
      
      // Get permission ID for the action
      const permissionId = getPermissionIdForAction(action);
      if (!permissionId) {
        return res.status(403).json({
          success: false,
          message: `Access forbidden: Invalid action ${action}`
        });
      }
      
      // Check if any of the user's active roles have this permission
      const hasPermission = await checkRoleModulePermission(activeRoleIds, module.id, permissionId);
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `Access forbidden: Cannot ${action} in ${moduleName} module`
        });
      }
      
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Error checking permissions'
      });
    }
  };
};

// Helper functions to query the database
async function getUserRolesFromDB(userId) {
  // Get all roles first to see what's available
  const allRolesQuery = `
    SELECT r.* 
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ?
  `;
  const allRoles = await db.query(allRolesQuery, [userId]);
  console.log(`All roles for user ${userId}:`, allRoles);
  
  // Then get only active roles
  const activeRolesQuery = `
    SELECT r.* 
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ? AND r.is_active = 1
  `;
  const activeRoles = await db.query(activeRolesQuery, [userId]);
  console.log(`Active roles for user ${userId}:`, activeRoles);
  
  return activeRoles;
}


async function getModuleByName(moduleName) {
  const query = 'SELECT * FROM modules WHERE name = ?';
  const modules = await db.query(query, [moduleName]);
  return modules[0];
}

function getPermissionIdForAction(action) {
  // Map action strings to permission IDs
  const actionMap = {
    'create': 1,
    'read': 2,
    'update': 3,
    'delete': 4
  };
  return actionMap[action] || null;
}

async function checkRoleModulePermission(roleIds, moduleId, permissionId) {
  if (roleIds.length === 0) return false;
  
  const placeholders = roleIds.map(() => '?').join(',');
  const query = `
    SELECT 1 FROM role_module_permissions
    WHERE role_id IN (${placeholders})
    AND module_id = ?
    AND permission_id = ?
    LIMIT 1
  `;
  
  const params = [...roleIds, moduleId, permissionId];
  console.log('Running query:', query);
  console.log('With params:', params);
  
  const result = await db.query(query, params);
  console.log('Query result:', result);
  
  return result.length > 0;
}

// Also log in helper functions
async function getModuleByName(moduleName) {
  console.log(`Looking up module: "${moduleName}"`);
  const query = 'SELECT * FROM modules WHERE name = ?';
  const modules = await db.query(query, [moduleName]);
  console.log(`Module lookup result:`, modules[0]);
  return modules[0];
}
module.exports = {
  verifyToken,
  hasModulePermission,
  hasAnyModulePermission,
  hasAllModulePermissions,
  hasRole,
  hasAnyRole,
  dynamicModulePermission
};