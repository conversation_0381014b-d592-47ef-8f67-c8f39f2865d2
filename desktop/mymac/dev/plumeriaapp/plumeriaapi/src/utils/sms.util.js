/**
 * SMS Utility
 * Handles SMS sending for OTP and notifications
 * 
 * You can integrate with any SMS service provider like:
 * - Twilio
 * - AWS SNS
 * - TextLocal
 * - MSG91
 * - Fast2SMS
 */

/**
 * Send SMS using your preferred SMS service
 * @param {string} mobile - Mobile number with country code
 * @param {string} message - SMS message content
 * @returns {Promise<boolean>} - Success status
 */
const sendSMS = async (mobile, message) => {
  try {
    // For development/testing - just log the SMS
    if (process.env.NODE_ENV === 'development') {
      console.log('=== SMS SENT (Development Mode) ===');
      console.log(`To: ${mobile}`);
      console.log(`Message: ${message}`);
      console.log('=====================================');
      return true;
    }

    // Production SMS implementation
    // Replace this with your actual SMS service integration
    
    // Example: Twilio integration
    if (process.env.SMS_SERVICE === 'twilio') {
      const twilio = require('twilio');
      const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
      
      await client.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: mobile
      });
      
      return true;
    }

    // Example: AWS SNS integration
    if (process.env.SMS_SERVICE === 'aws-sns') {
      const AWS = require('aws-sdk');
      const sns = new AWS.SNS({
        region: process.env.AWS_REGION,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      });

      await sns.publish({
        Message: message,
        PhoneNumber: mobile
      }).promise();

      return true;
    }

    // Example: TextLocal integration (India)
    if (process.env.SMS_SERVICE === 'textlocal') {
      const axios = require('axios');
      
      const response = await axios.post('https://api.textlocal.in/send/', {
        apikey: process.env.TEXTLOCAL_API_KEY,
        numbers: mobile,
        message: message,
        sender: process.env.TEXTLOCAL_SENDER_ID
      });

      return response.data.status === 'success';
    }

    // Example: MSG91 integration (India)
    if (process.env.SMS_SERVICE === 'msg91') {
      const axios = require('axios');
      
      const response = await axios.post(`https://api.msg91.com/api/v5/otp`, {
        template_id: process.env.MSG91_TEMPLATE_ID,
        mobile: mobile,
        authkey: process.env.MSG91_AUTH_KEY,
        message: message
      });

      return response.data.type === 'success';
    }

    // Default: Log warning if no SMS service configured
    console.warn('No SMS service configured. SMS not sent.');
    console.log(`Would send to ${mobile}: ${message}`);
    return false;

  } catch (error) {
    console.error('SMS sending failed:', error);
    throw new Error('Failed to send SMS');
  }
};

/**
 * Send OTP SMS with formatted message
 * @param {string} mobile - Mobile number
 * @param {string} otp - OTP code
 * @param {number} expiryMinutes - OTP expiry in minutes
 * @returns {Promise<boolean>} - Success status
 */
const sendOTPSMS = async (mobile, otp, expiryMinutes = 10) => {
  const message = `Your OTP for login is: ${otp}. Valid for ${expiryMinutes} minutes. Do not share this OTP with anyone.`;
  return await sendSMS(mobile, message);
};

/**
 * Send password reset SMS
 * @param {string} mobile - Mobile number
 * @param {string} resetCode - Reset code
 * @param {number} expiryMinutes - Reset code expiry in minutes
 * @returns {Promise<boolean>} - Success status
 */
const sendPasswordResetSMS = async (mobile, resetCode, expiryMinutes = 30) => {
  const message = `Your password reset code is: ${resetCode}. Valid for ${expiryMinutes} minutes. Use this code to reset your password.`;
  return await sendSMS(mobile, message);
};

/**
 * Send welcome SMS to new staff
 * @param {string} mobile - Mobile number
 * @param {string} staffName - Staff name
 * @returns {Promise<boolean>} - Success status
 */
const sendWelcomeSMS = async (mobile, staffName) => {
  const message = `Welcome ${staffName}! Your staff account has been created. Download our mobile app to get started.`;
  return await sendSMS(mobile, message);
};

/**
 * Validate mobile number format
 * @param {string} mobile - Mobile number to validate
 * @returns {boolean} - Is valid mobile number
 */
const isValidMobile = (mobile) => {
  // Basic mobile number validation (adjust regex based on your country)
  const mobileRegex = /^[+]?[1-9]\d{1,14}$/; // International format
  const indianMobileRegex = /^[+]?91[6-9]\d{9}$/; // Indian mobile format
  
  return mobileRegex.test(mobile) || indianMobileRegex.test(mobile);
};

/**
 * Format mobile number for SMS sending
 * @param {string} mobile - Raw mobile number
 * @returns {string} - Formatted mobile number
 */
const formatMobile = (mobile) => {
  // Remove all non-digit characters except +
  let formatted = mobile.replace(/[^\d+]/g, '');
  
  // Add country code if not present (assuming India +91)
  if (!formatted.startsWith('+')) {
    if (formatted.length === 10) {
      formatted = '+91' + formatted;
    } else if (formatted.length === 12 && formatted.startsWith('91')) {
      formatted = '+' + formatted;
    }
  }
  
  return formatted;
};

module.exports = {
  sendSMS,
  sendOTPSMS,
  sendPasswordResetSMS,
  sendWelcomeSMS,
  isValidMobile,
  formatMobile
};
