-- Create staffs table for staff management
-- This table stores comprehensive staff information for the construction company

CREATE TABLE IF NOT EXISTS `staffs` (
  `id` int NOT NULL AUTO_INCREMENT,
  
  -- Basic Information
  `staff_name` varchar(255) NOT NULL,
  `staff_mobile` varchar(15) NOT NULL,
  `staff_email` varchar(255) NOT NULL,
  `gender` enum('Male', 'Female', 'Other') NOT NULL,
  `date_of_birth` date NOT NULL,
  `marital_status` enum('Single', 'Married', 'Divorced', 'Widowed') NOT NULL,
  
  -- Professional Information
  `designation_id` int NOT NULL,
  `department_id` int NOT NULL,
  `employment_type_id` int NOT NULL,
  `joining_date` date NOT NULL,
  `salary_amount` decimal(10,2) DEFAULT NULL,
  `salary_last_hiked_date` date DEFAULT NULL,
  
  -- Contact Information
  `emergency_contact_name` varchar(255) NOT NULL,
  `emergency_contact_phone` varchar(15) NOT NULL,
  `emergency_contact_relation` varchar(100) NOT NULL,
  
  -- Address Information
  `staff_address` text NOT NULL,
  `locality_id` int NOT NULL,
  `city_id` int NOT NULL,
  `state_id` int NOT NULL,
  `pincode` varchar(10) NOT NULL,
  
  -- Identity Documents
  `aadhaar_number` varchar(12) DEFAULT NULL,
  `pan_number` varchar(10) DEFAULT NULL,
  
  -- Banking Information
  `bank_name` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL,
  `ifsc_code` varchar(11) DEFAULT NULL,
  
  -- Health Insurance
  `health_insurance_provider` varchar(255) DEFAULT NULL,
  `health_insurance_number` varchar(100) DEFAULT NULL,
  
  -- Additional Information
  `blood_group_id` int DEFAULT NULL,
  `qualification_id` int DEFAULT NULL,
  `user_role_id` int DEFAULT NULL,
  `profile_picture` varchar(500) DEFAULT NULL,
  
  -- Status and Audit
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  
  -- Unique constraints
  UNIQUE KEY `unique_staff_email` (`staff_email`),
  UNIQUE KEY `unique_staff_mobile` (`staff_mobile`),
  UNIQUE KEY `unique_aadhaar` (`aadhaar_number`),
  UNIQUE KEY `unique_pan` (`pan_number`),
  
  -- Foreign key constraints
  KEY `fk_staff_designation` (`designation_id`),
  KEY `fk_staff_department` (`department_id`),
  KEY `fk_staff_employment_type` (`employment_type_id`),
  KEY `fk_staff_locality` (`locality_id`),
  KEY `fk_staff_city` (`city_id`),
  KEY `fk_staff_state` (`state_id`),
  KEY `fk_staff_blood_group` (`blood_group_id`),
  KEY `fk_staff_qualification` (`qualification_id`),
  KEY `fk_staff_user_role` (`user_role_id`),
  KEY `fk_staff_created_by` (`created_by`),
  KEY `fk_staff_updated_by` (`updated_by`),
  
  CONSTRAINT `fk_staff_designation` FOREIGN KEY (`designation_id`) REFERENCES `designations` (`id`),
  CONSTRAINT `fk_staff_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `fk_staff_employment_type` FOREIGN KEY (`employment_type_id`) REFERENCES `employment_types` (`id`),
  CONSTRAINT `fk_staff_locality` FOREIGN KEY (`locality_id`) REFERENCES `localities` (`id`),
  CONSTRAINT `fk_staff_city` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`),
  CONSTRAINT `fk_staff_state` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`),
  CONSTRAINT `fk_staff_blood_group` FOREIGN KEY (`blood_group_id`) REFERENCES `blood_groups` (`id`),
  CONSTRAINT `fk_staff_qualification` FOREIGN KEY (`qualification_id`) REFERENCES `qualifications` (`id`),
  CONSTRAINT `fk_staff_user_role` FOREIGN KEY (`user_role_id`) REFERENCES `roles` (`id`),
  CONSTRAINT `fk_staff_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_staff_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Create indexes for better performance
CREATE INDEX `idx_staff_active` ON `staffs` (`is_active`);
CREATE INDEX `idx_staff_department_active` ON `staffs` (`department_id`, `is_active`);
CREATE INDEX `idx_staff_designation_active` ON `staffs` (`designation_id`, `is_active`);
CREATE INDEX `idx_staff_name` ON `staffs` (`staff_name`);
CREATE INDEX `idx_staff_email` ON `staffs` (`staff_email`);
CREATE INDEX `idx_staff_mobile` ON `staffs` (`staff_mobile`);
