const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const authRoutes = require('./src/routes/auth.routes');
const roleRoutes = require('./src/routes/role.routes');
const moduleRoutes = require('./src/routes/module.routes');
const userRoutes = require('./src/routes/user.routes');
const systemRoutes = require('./src/routes/system.routes');
const permissionRoutes = require('./src/routes/permission.routes');

//masters
const mastersRoutes = require('./src/routes/masters');

//staff management
const staffRoutes = require('./src/routes/staff.routes');

let roleModulePermissionRoutes;
try {
  roleModulePermissionRoutes = require('./src/routes/role_module_permission.routes');
} catch (error) {
  console.warn('Role module permission routes not available yet:', error.message);
}

const app = express();

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (for uploaded images)
app.use('/uploads', express.static('src/public/uploads'));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/modules', moduleRoutes);
app.use('/api/users', userRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/permissions', permissionRoutes);

//master routes
app.use('/api/masters', mastersRoutes);

//staff management routes
app.use('/api/staffs', staffRoutes);

// Conditionally add role_module_permission routes if available
if (roleModulePermissionRoutes) {
  app.use('/api/role-module-permissions', roleModulePermissionRoutes);
}

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

module.exports = app;