const fs = require('fs');
const FormData = require('form-data');
const axios = require('axios');

// Test script to verify profile picture upload
async function testUpload() {
  try {
    console.log('Testing profile picture upload...');
    
    // First, let's test if we can get a staff member
    const staffResponse = await axios.get('http://localhost:3000/api/staffs', {
      headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // You'll need to replace this with a real token
      }
    });
    
    console.log('Staff API response:', staffResponse.status);
    
    if (staffResponse.data.success && staffResponse.data.data.length > 0) {
      const staffId = staffResponse.data.data[0].id;
      console.log('Testing upload for staff ID:', staffId);
      
      // Create a simple test image file (1x1 pixel PNG)
      const testImageBuffer = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
        0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
        0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ]);
      
      // Create form data
      const form = new FormData();
      form.append('profile_picture', testImageBuffer, {
        filename: 'test.png',
        contentType: 'image/png'
      });
      
      // Test upload
      const uploadResponse = await axios.post(
        `http://localhost:3000/api/staffs/${staffId}/upload-photo`,
        form,
        {
          headers: {
            ...form.getHeaders(),
            'Authorization': 'Bearer YOUR_TOKEN_HERE' // You'll need to replace this with a real token
          }
        }
      );
      
      console.log('Upload response:', uploadResponse.data);
    }
    
  } catch (error) {
    console.error('Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testUpload();
