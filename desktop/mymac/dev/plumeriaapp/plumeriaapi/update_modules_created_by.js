/**
 * <PERSON><PERSON><PERSON> to update existing modules with created_by field
 * 
 * This script will set the created_by field for all existing modules
 * to the ID of the admin user (usually ID 1).
 */

const db = require('./src/config/database');

async function updateModulesCreatedBy() {
  try {
    console.log('Starting update of modules created_by field...');
    
    // Get admin user ID (assuming admin user has ID 1)
    const adminUserId = 1;
    
    // Update all modules to set created_by to admin user ID
    const updateQuery = 'UPDATE modules SET created_by = ? WHERE created_by IS NULL';
    const result = await db.query(updateQuery, [adminUserId]);
    
    console.log(`Updated ${result.affectedRows} modules with created_by = ${adminUserId}`);
    console.log('Update completed successfully!');
    
    process.exit(0);
  } catch (error) {
    console.error('Error updating modules:', error);
    process.exit(1);
  }
}

// Run the update function
updateModulesCreatedBy();
