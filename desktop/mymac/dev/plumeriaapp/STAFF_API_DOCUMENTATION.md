# Staff Management API Documentation

## Overview
Professional staff management API for construction company employee records with comprehensive CRUD operations, file upload capabilities, and advanced filtering.

## Base URL
```
http://localhost:3000/api/staffs
```

## Authentication
All endpoints require JWT authentication via `Authorization: Bearer <token>` header.

## Permissions
All endpoints require appropriate permissions for the `staff` module:
- `create` - Create new staff records
- `read` - View staff records
- `update` - Modify staff records
- `delete` - Remove staff records

---

## Endpoints

### 1. Get All Staff
**GET** `/api/staffs`

Retrieve paginated list of staff with filtering and search capabilities.

**Query Parameters:**
- `page` (number, optional) - Page number (default: 1)
- `limit` (number, optional) - Items per page (default: 10)
- `search` (string, optional) - Search in name, email, mobile, department, designation
- `departmentId` (number, optional) - Filter by department
- `designationId` (number, optional) - Filter by designation
- `includeInactive` (boolean, optional) - Include inactive staff (default: false)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "staff_name": "<PERSON>",
      "staff_email": "<EMAIL>",
      "staff_mobile": "**********",
      "gender": "Male",
      "department_name": "Engineering",
      "designation_name": "Site Engineer",
      "is_active": true,
      "profile_picture": "uploads/staff/1_uuid.jpg",
      // ... other fields
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalCount": 45,
    "limit": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 2. Get Staff by ID
**GET** `/api/staffs/:id`

Retrieve detailed information for a specific staff member.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "staff_name": "John Doe",
    "staff_mobile": "**********",
    "staff_email": "<EMAIL>",
    "gender": "Male",
    "user_role_name": "Employee",
    "date_of_birth": "1990-05-15",
    "marital_status": "Married",
    "blood_group_name": "O+",
    "joining_date": "2023-01-15",
    "emergency_contact_name": "Jane Doe",
    "emergency_contact_phone": "**********",
    "emergency_contact_relation": "Spouse",
    "designation_name": "Site Engineer",
    "employment_type_name": "Permanent",
    "department_name": "Engineering",
    "salary_amount": 50000.00,
    "staff_address": "123 Main Street, City",
    "locality_name": "Downtown",
    "city_name": "Mumbai",
    "state_name": "Maharashtra",
    "pincode": "400001",
    "aadhaar_number": "************",
    "pan_number": "**********",
    "qualification_name": "B.Tech Civil",
    "bank_name": "HDFC Bank",
    "account_number": "************34",
    "ifsc_code": "HDFC0001234",
    "profile_picture": "uploads/staff/1_uuid.jpg",
    "is_active": true,
    "created_at": "2023-01-15T10:30:00.000Z",
    "updated_at": "2023-01-15T10:30:00.000Z"
  }
}
```

### 3. Create Staff
**POST** `/api/staffs`

Create a new staff member record.

**Request Body:**
```json
{
  "staff_name": "John Doe",
  "staff_mobile": "**********",
  "staff_email": "<EMAIL>",
  "gender": "Male",
  "user_role_id": 2,
  "date_of_birth": "1990-05-15",
  "marital_status": "Married",
  "blood_group_id": 1,
  "joining_date": "2023-01-15",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_phone": "**********",
  "emergency_contact_relation": "Spouse",
  "designation_id": 1,
  "employment_type_id": 1,
  "health_insurance_provider": "Star Health",
  "health_insurance_number": "SH123456789",
  "department_id": 1,
  "salary_amount": 50000.00,
  "salary_last_hiked_date": "2023-01-15",
  "staff_address": "123 Main Street, City",
  "locality_id": 1,
  "city_id": 1,
  "pincode": "400001",
  "state_id": 1,
  "aadhaar_number": "************",
  "pan_number": "**********",
  "qualification_id": 1,
  "bank_name": "HDFC Bank",
  "account_number": "************34",
  "ifsc_code": "HDFC0001234"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Staff member created successfully",
  "data": {
    "id": 1,
    // ... created staff data
  }
}
```

### 4. Update Staff
**PUT** `/api/staffs/:id`

Update an existing staff member record.

**Request Body:** Same as create, but all fields are optional.

### 5. Delete Staff
**DELETE** `/api/staffs/:id`

Delete a staff member record.

**Response:**
```json
{
  "success": true,
  "message": "Staff member deleted successfully"
}
```

### 6. Toggle Staff Status
**PATCH** `/api/staffs/:id/status`

Activate or deactivate a staff member.

**Request Body:**
```json
{
  "is_active": false
}
```

### 7. Bulk Delete Staff
**POST** `/api/staffs/bulk-delete`

Delete multiple staff members at once.

**Request Body:**
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

### 8. Get Staff by Department
**GET** `/api/staffs/department/:departmentId`

Get all staff members in a specific department.

**Query Parameters:**
- `includeInactive` (boolean, optional) - Include inactive staff

### 9. Upload Profile Picture
**POST** `/api/staffs/:id/upload-photo`

Upload a profile picture for a staff member.

**Content-Type:** `multipart/form-data`
**Form Field:** `profile_picture` (file)

**File Requirements:**
- Formats: JPEG, PNG, WebP
- Max size: 2MB
- Single file only

**Response:**
```json
{
  "success": true,
  "message": "Profile picture uploaded successfully",
  "data": {
    "id": 1,
    "profile_picture": "uploads/staff/1_uuid.jpg"
  }
}
```

### 10. Remove Profile Picture
**DELETE** `/api/staffs/:id/photo`

Remove the profile picture for a staff member.

---

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "Staff name is required and must be at least 2 characters",
    "Valid email address is required"
  ]
}
```

### Not Found (404)
```json
{
  "success": false,
  "message": "Staff member not found"
}
```

### Duplicate Entry (409)
```json
{
  "success": false,
  "message": "Staff member with this email already exists"
}
```

### Server Error (500)
```json
{
  "success": false,
  "message": "Error creating staff record",
  "error": "Database connection failed"
}
```

---

## Data Validation Rules

### Required Fields (Create)
- `staff_name` (min: 2 chars)
- `staff_mobile` (10 digits, starts with 6-9)
- `staff_email` (valid email format)
- `gender` (Male/Female/Other)
- `date_of_birth` (valid date, age ≥ 18)
- `marital_status` (Single/Married/Divorced/Widowed)
- `joining_date` (valid date)
- `emergency_contact_name` (min: 2 chars)
- `emergency_contact_phone` (valid mobile)
- `emergency_contact_relation` (min: 2 chars)
- `designation_id` (valid ID)
- `employment_type_id` (valid ID)
- `department_id` (valid ID)
- `staff_address` (min: 10 chars)
- `locality_id` (valid ID)
- `city_id` (valid ID)
- `state_id` (valid ID)
- `pincode` (6 digits)

### Optional Fields with Validation
- `aadhaar_number` (12 digits)
- `pan_number` (********** format)
- `ifsc_code` (ABCD0123456 format)
- `salary_amount` (positive number)

---

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install multer uuid
   ```

2. **Create Database Table:**
   ```sql
   -- Run the staffs table creation SQL
   ```

3. **Add Staff Module:**
   ```sql
   -- Run staff_module_setup.sql
   ```

4. **Create Upload Directory:**
   ```bash
   mkdir -p src/public/uploads/staff
   ```

5. **Update Environment:**
   Ensure your `.env` file has proper database configuration.

---

## File Structure
```
src/
├── models/staff.model.js
├── controllers/staff.controller.js
├── services/staff.service.js
├── routes/staff.routes.js
├── middleware/upload.middleware.js
└── public/uploads/staff/
```

This API provides a complete, professional staff management solution with robust validation, file handling, and comprehensive CRUD operations.
